/****************************************************************************
** Meta object code from reading C++ file 'ConnectionDialog.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../ConnectionDialog.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConnectionDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ConnectionDialog_t {
    QByteArrayData data[20];
    char stringdata0[309];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ConnectionDialog_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ConnectionDialog_t qt_meta_stringdata_ConnectionDialog = {
    {
QT_MOC_LITERAL(0, 0, 16), // "ConnectionDialog"
QT_MOC_LITERAL(1, 17, 19), // "connectionRequested"
QT_MOC_LITERAL(2, 37, 0), // ""
QT_MOC_LITERAL(3, 38, 6), // "params"
QT_MOC_LITERAL(4, 45, 23), // "testConnectionRequested"
QT_MOC_LITERAL(5, 69, 14), // "testConnection"
QT_MOC_LITERAL(6, 84, 15), // "connectToDevice"
QT_MOC_LITERAL(7, 100, 12), // "onTestResult"
QT_MOC_LITERAL(8, 113, 7), // "success"
QT_MOC_LITERAL(9, 121, 7), // "message"
QT_MOC_LITERAL(10, 129, 21), // "refreshCurrentDevices"
QT_MOC_LITERAL(11, 151, 23), // "onConnectionTypeChanged"
QT_MOC_LITERAL(12, 175, 19), // "onSerialPortRefresh"
QT_MOC_LITERAL(13, 195, 18), // "onADBDeviceRefresh"
QT_MOC_LITERAL(14, 214, 17), // "onAdvancedToggled"
QT_MOC_LITERAL(15, 232, 4), // "show"
QT_MOC_LITERAL(16, 237, 21), // "onHistoryItemSelected"
QT_MOC_LITERAL(17, 259, 15), // "onSaveToHistory"
QT_MOC_LITERAL(18, 275, 19), // "onDeleteFromHistory"
QT_MOC_LITERAL(19, 295, 13) // "onTestTimeout"

    },
    "ConnectionDialog\0connectionRequested\0"
    "\0params\0testConnectionRequested\0"
    "testConnection\0connectToDevice\0"
    "onTestResult\0success\0message\0"
    "refreshCurrentDevices\0onConnectionTypeChanged\0"
    "onSerialPortRefresh\0onADBDeviceRefresh\0"
    "onAdvancedToggled\0show\0onHistoryItemSelected\0"
    "onSaveToHistory\0onDeleteFromHistory\0"
    "onTestTimeout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ConnectionDialog[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   84,    2, 0x06 /* Public */,
       4,    1,   87,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   90,    2, 0x0a /* Public */,
       6,    0,   91,    2, 0x0a /* Public */,
       7,    2,   92,    2, 0x0a /* Public */,
      10,    0,   97,    2, 0x0a /* Public */,
      11,    0,   98,    2, 0x08 /* Private */,
      12,    0,   99,    2, 0x08 /* Private */,
      13,    0,  100,    2, 0x08 /* Private */,
      14,    1,  101,    2, 0x08 /* Private */,
      16,    0,  104,    2, 0x08 /* Private */,
      17,    0,  105,    2, 0x08 /* Private */,
      18,    0,  106,    2, 0x08 /* Private */,
      19,    0,  107,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QVariantMap,    3,
    QMetaType::Void, QMetaType::QVariantMap,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,    8,    9,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ConnectionDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConnectionDialog *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->connectionRequested((*reinterpret_cast< const QVariantMap(*)>(_a[1]))); break;
        case 1: _t->testConnectionRequested((*reinterpret_cast< const QVariantMap(*)>(_a[1]))); break;
        case 2: _t->testConnection(); break;
        case 3: _t->connectToDevice(); break;
        case 4: _t->onTestResult((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 5: _t->refreshCurrentDevices(); break;
        case 6: _t->onConnectionTypeChanged(); break;
        case 7: _t->onSerialPortRefresh(); break;
        case 8: _t->onADBDeviceRefresh(); break;
        case 9: _t->onAdvancedToggled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 10: _t->onHistoryItemSelected(); break;
        case 11: _t->onSaveToHistory(); break;
        case 12: _t->onDeleteFromHistory(); break;
        case 13: _t->onTestTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ConnectionDialog::*)(const QVariantMap & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionDialog::connectionRequested)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ConnectionDialog::*)(const QVariantMap & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionDialog::testConnectionRequested)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ConnectionDialog::staticMetaObject = { {
    &QDialog::staticMetaObject,
    qt_meta_stringdata_ConnectionDialog.data,
    qt_meta_data_ConnectionDialog,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ConnectionDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConnectionDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ConnectionDialog.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int ConnectionDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 14;
    }
    return _id;
}

// SIGNAL 0
void ConnectionDialog::connectionRequested(const QVariantMap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ConnectionDialog::testConnectionRequested(const QVariantMap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
