#ifndef COMMANDLISTWIDGET_H
#define COMMANDLISTWIDGET_H

#include <QListWidget>
#include <QContextMenuEvent>
#include <QMenu>
#include <QAction>
#include <QVariantMap>
#include <QTimer>
#include <QDateTime>

class CommandListWidget : public QListWidget
{
    Q_OBJECT

public:
    struct CommandItem
    {
        QString name;
        QString content;
        QString description;
        bool enabled;
        QDateTime created;
        QDateTime lastUsed;
        int usageCount;
        QVariantMap metadata;
    };

    explicit CommandListWidget(QWidget *parent = nullptr);
    ~CommandListWidget();

    // 命令管理
    void addCommand(const QString &name, const QString &content, const QString &description = QString());
    void removeCommand(const QString &name);
    void updateCommand(const QString &name, const QString &newName, const QString &content, const QString &description = QString());
    void clearCommands();

    // 命令操作
    void enableCommand(const QString &name, bool enabled = true);
    void disableCommand(const QString &name);
    bool isCommandEnabled(const QString &name) const;

    // 数据访问
    QStringList getCommandNames() const;
    CommandItem getCommand(const QString &name) const;
    QList<CommandItem> getAllCommands() const;
    int commandCount() const;

    // 搜索和过滤
    void setFilter(const QString &filter);
    QString currentFilter() const;
    void clearFilter();

    // 排序
    enum SortOrder
    {
        ByName,
        ByCreated,
        ByLastUsed,
        ByUsageCount
    };
    void setSortOrder(SortOrder order, Qt::SortOrder direction = Qt::AscendingOrder);
    SortOrder currentSortOrder() const;

    // 导入导出
    bool exportCommands(const QString &filePath) const;
    bool importCommands(const QString &filePath, bool merge = true);
    QVariantList toVariantList() const;
    void fromVariantList(const QVariantList &commands, bool merge = true);

    // 外观配置
    void setShowDescription(bool show);
    bool isShowDescription() const;
    void setShowUsageCount(bool show);
    bool isShowUsageCount() const;

signals:
    void executeCommand(const QString &name, const QString &content);
    void commandAdded(const QString &name, const QString &content);
    void commandEdited(const QString &oldName, const QString &newName, const QString &newContent);
    void commandDeleted(const QString &name);
    void commandEnabled(const QString &name, bool enabled);

protected:
    void contextMenuEvent(QContextMenuEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;

private slots:
    void onExecuteCommand();
    void onEditCommand();
    void onDeleteCommand();
    void onToggleCommand();
    void onCopyCommand();
    void onMoveUp();
    void onMoveDown();
    void onShowProperties();
    void onFilterTimer();

private:
    void setupContextMenu();
    void updateDisplay();
    void updateItem(QListWidgetItem *item, const CommandItem &command);
    QString formatItemText(const CommandItem &command) const;
    QListWidgetItem *findItemByName(const QString &name) const;
    void sortCommands();
    void applyFilter();
    CommandItem *getCommandData(QListWidgetItem *item) const;
    void setCommandData(QListWidgetItem *item, const CommandItem &command);

private:
    // 命令数据
    QMap<QString, CommandItem> m_commands;

    // 上下文菜单
    QMenu *m_contextMenu;
    QAction *m_executeAction;
    QAction *m_editAction;
    QAction *m_deleteAction;
    QAction *m_toggleAction;
    QAction *m_copyAction;
    QAction *m_moveUpAction;
    QAction *m_moveDownAction;
    QAction *m_propertiesAction;

    // 过滤和排序
    QString m_filter;
    SortOrder m_sortOrder;
    Qt::SortOrder m_sortDirection;
    QTimer *m_filterTimer;

    // 显示选项
    bool m_showDescription;
    bool m_showUsageCount;

    // 当前选中的命令
    QString m_currentCommand;
};

#endif // COMMANDLISTWIDGET_H
