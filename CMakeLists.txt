cmake_minimum_required(VERSION 3.16)

project(RFTool_Qt VERSION 2.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets SerialPort Network)

# 启用Qt的MOC、UIC和RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 源文件
set(SOURCES
    main.cpp
    MainWindow.cpp
    ConnectionManager.cpp
    ConfigManager.cpp
    LogManager.cpp
    InteractiveTerminal.cpp
    CommandHistory.cpp
    CommandListWidget.cpp
    SerialConnection.cpp
    ConnectionDialog.cpp
    LogConfigDialog.cpp
    BackgroundConfigDialog.cpp
)

# 头文件
set(HEADERS
    MainWindow.h
    ConnectionManager.h
    ConfigManager.h
    LogManager.h
    InteractiveTerminal.h
    CommandHistory.h
    CommandListWidget.h
    SerialConnection.h
    ConnectionDialog.h
    LogConfigDialog.h
    BackgroundConfigDialog.h
)

# 创建可执行文件
add_executable(RFTool_Qt ${SOURCES} ${HEADERS})

# 链接Qt库
target_link_libraries(RFTool_Qt
    Qt6::Core
    Qt6::Widgets
    Qt6::SerialPort
    Qt6::Network
)

# 设置输出目录
set_target_properties(RFTool_Qt PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Windows特定设置
if(WIN32)
    set_target_properties(RFTool_Qt PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()

# 安装规则
install(TARGETS RFTool_Qt
    RUNTIME DESTINATION bin
)
