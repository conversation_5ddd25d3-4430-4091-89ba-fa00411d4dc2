#ifndef INTERACTIVETERMINAL_H
#define INTERACTIVETERMINAL_H

#include <QTextEdit>
#include <QKeyEvent>
#include <QContextMenuEvent>
#include <QMenu>
#include <QAction>
#include <QStringList>
#include <QTimer>
#include <QDateTime>
#include <QTextCharFormat>
#include <QScrollBar>

#include "LogManager.h"

class InteractiveTerminal : public QTextEdit
{
    Q_OBJECT

public:
    explicit InteractiveTerminal(QWidget *parent = nullptr);
    ~InteractiveTerminal();

    // 消息追加
    void appendMessage(const QString &message, LogManager::LogLevel level = LogManager::Info);
    void appendSystemOutput(const QString &output);
    void appendUserInput(const QString &input);
    void appendError(const QString &error);

    // 终端控制
    void clearTerminal();
    void setPrompt(const QString &prompt);
    QString currentPrompt() const;
    void setEchoEnabled(bool enabled);
    bool isEchoEnabled() const;

    // 时间戳配置
    void setTimestampEnabled(bool enabled);
    bool isTimestampEnabled() const;
    void setTimestampFormat(const QString &format);
    QString timestampFormat() const;

    // 日志存储配置
    void setLogStorageEnabled(bool enabled);
    bool isLogStorageEnabled() const;
    void setLogFilePath(const QString &filePath);
    QString logFilePath() const;

    // 命令历史
    void setMaxHistorySize(int size);
    int maxHistorySize() const;
    QStringList commandHistory() const;
    void clearHistory();

    // 外观配置
    void setBackgroundColor(const QColor &color);
    void setTextColor(const QColor &color);
    void setFont(const QFont &font);
    void applyColorScheme(const QString &scheme);

signals:
    void commandEntered(const QString &command);
    void logMessage(const QString &message, LogManager::LogLevel level);

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;

private slots:
    void onCursorPositionChanged();
    void onTextChanged();
    void saveToLogFile();

private:
    void setupTerminal();
    void setupContextMenu();
    void setupColorFormats();
    
    void insertPrompt();
    void processCommand();
    void navigateHistory(bool up);
    
    QString getCurrentLine() const;
    QString getCurrentCommand() const;
    void setCurrentCommand(const QString &command);
    
    bool isAtPromptLine() const;
    bool isInEditableArea() const;
    int getPromptPosition() const;
    int getEditableStartPosition() const;
    
    void moveCursorToEnd();
    void moveCursorToPromptEnd();
    
    QString formatMessage(const QString &message, LogManager::LogLevel level) const;
    QString formatTimestamp() const;
    QTextCharFormat getFormatForLevel(LogManager::LogLevel level) const;
    
    void appendFormattedText(const QString &text, const QTextCharFormat &format);
    void scrollToBottom();
    
    void copySelectedText();
    void pasteText();
    void selectAll();
    void clearScreen();

private:
    // 提示符和输入
    QString m_prompt;
    QString m_defaultPrompt;
    int m_promptPosition;
    bool m_echoEnabled;
    
    // 命令历史
    QStringList m_commandHistory;
    int m_historyIndex;
    int m_maxHistorySize;
    QString m_currentCommand;
    
    // 时间戳配置
    bool m_timestampEnabled;
    QString m_timestampFormat;
    
    // 日志存储
    bool m_logStorageEnabled;
    QString m_logFilePath;
    QTimer *m_logSaveTimer;
    QStringList m_pendingLogMessages;
    
    // 文本格式
    QTextCharFormat m_infoFormat;
    QTextCharFormat m_warningFormat;
    QTextCharFormat m_errorFormat;
    QTextCharFormat m_systemFormat;
    QTextCharFormat m_inputFormat;
    QTextCharFormat m_outputFormat;
    QTextCharFormat m_promptFormat;
    
    // 上下文菜单
    QMenu *m_contextMenu;
    QAction *m_copyAction;
    QAction *m_pasteAction;
    QAction *m_selectAllAction;
    QAction *m_clearAction;
    QAction *m_saveLogAction;
    
    // 状态标志
    bool m_isProcessingInput;
    bool m_isUpdatingText;
    
    // 颜色方案
    QMap<QString, QVariantMap> m_colorSchemes;
    QString m_currentColorScheme;
};

#endif // INTERACTIVETERMINAL_H
