#ifndef TCPCONNECTION_H
#define TCPCONNECTION_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QVariantMap>
#include <QMutex>
#include <QQueue>
#include <QDateTime>
#include <QHostAddress>

class TCPConnection : public QObject
{
    Q_OBJECT

public:
    enum ConnectionState
    {
        Disconnected,
        Connecting,
        Connected,
        Error
    };

    enum Protocol
    {
        TCP,
        UDP
    };

    explicit TCPConnection(QObject *parent = nullptr);
    ~TCPConnection();

    // 连接管理
    bool connect(const QVariantMap &params);
    void disconnect();
    bool isConnected() const;
    ConnectionState state() const;
    QString stateString() const;

    // 数据传输
    bool sendCommand(const QString &command);
    bool sendData(const QByteArray &data);

    // 配置
    void setHost(const QString &host);
    QString host() const;
    void setPort(quint16 port);
    quint16 port() const;
    void setProtocol(Protocol protocol);
    Protocol protocol() const;

    // 连接选项
    void setConnectionTimeout(int seconds);
    int connectionTimeout() const;
    void setAutoReconnect(bool enabled);
    bool autoReconnectEnabled() const;
    void setKeepAlive(bool enabled);
    bool keepAliveEnabled() const;

    // 数据处理选项
    void setLineEnding(const QString &ending);
    QString lineEnding() const;
    void setEncoding(const QString &encoding);
    QString encoding() const;

    // 统计信息
    qint64 bytesReceived() const;
    qint64 bytesSent() const;
    QDateTime connectionTime() const;
    int commandCount() const;

public slots:
    void reconnect();

signals:
    void connected();
    void disconnected();
    void dataReceived(const QByteArray &data);
    void errorOccurred(const QString &error);
    void stateChanged(ConnectionState state);

private slots:
    void onSocketConnected();
    void onSocketDisconnected();
    void onSocketError(QAbstractSocket::SocketError error);
    void onSocketReadyRead();
    void onConnectionTimeout();
    void onKeepAliveTimer();

private:
    void setState(ConnectionState state);
    void processReceivedData();
    QString formatError(const QString &error) const;
    void cleanup();

private:
    // 网络连接
    QTcpSocket *m_socket;
    QString m_host;
    quint16 m_port;
    Protocol m_protocol;
    
    // 连接状态
    ConnectionState m_state;
    bool m_isConnected;
    QDateTime m_connectionTime;
    
    // 连接选项
    int m_connectionTimeout;
    bool m_autoReconnectEnabled;
    bool m_keepAliveEnabled;
    int m_keepAliveInterval;
    
    // 数据处理
    QString m_lineEnding;
    QString m_encoding;
    QByteArray m_receiveBuffer;
    
    // 定时器
    QTimer *m_connectionTimer;
    QTimer *m_keepAliveTimer;
    
    // 统计信息
    qint64 m_bytesReceived;
    qint64 m_bytesSent;
    int m_commandCount;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 命令队列
    QQueue<QByteArray> m_sendQueue;
    bool m_isSending;
};

#endif // TCPCONNECTION_H
