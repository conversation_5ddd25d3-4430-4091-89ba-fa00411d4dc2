#include "SSHConnection.h"
#include <QMutexLocker>
#include <QDebug>
#include <QCryptographicHash>
#include <QFile>
#include <QStandardPaths>
#include <QTimer>
#include <QTcpSocket>
#include <QDateTime>

SSHConnection::SSHConnection(QObject *parent)
    : QObject(parent), m_socket(nullptr), m_host("localhost"), m_port(22), m_authMethod(Password), m_state(Disconnected), m_isConnected(false), m_connectionTimeout(30), m_keepAliveEnabled(true), m_keepAliveInterval(60), m_terminalType("xterm"), m_terminalSize(80, 24), m_bytesReceived(0), m_bytesSent(0), m_commandCount(0), m_protocolInitialized(false), m_authenticated(false)
{
    // 创建TCP套接字
    m_socket = new QTcpSocket(this);
    QObject::connect(m_socket, &QTcpSocket::connected, this, &SSHConnection::onSocketConnected);
    QObject::connect(m_socket, &QTcpSocket::disconnected, this, &SSHConnection::onSocketDisconnected);
    QObject::connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
                     this, &SSHConnection::onSocketError);
    QObject::connect(m_socket, &QTcpSocket::readyRead, this, &SSHConnection::onSocketReadyRead);

    // 创建定时器
    m_connectionTimer = new QTimer(this);
    m_connectionTimer->setSingleShot(true);
    QObject::connect(m_connectionTimer, &QTimer::timeout, this, &SSHConnection::onConnectionTimeout);

    m_keepAliveTimer = new QTimer(this);
    QObject::connect(m_keepAliveTimer, &QTimer::timeout, this, &SSHConnection::onKeepAliveTimer);
}

SSHConnection::~SSHConnection()
{
    disconnect();
}

bool SSHConnection::connect(const QVariantMap &params)
{
    QMutexLocker locker(&m_mutex);

    if (m_isConnected || m_state == Connecting)
    {
        return false;
    }

    // 解析连接参数
    m_host = params.value("host", "localhost").toString();
    m_port = params.value("port", 22).toInt();
    m_username = params.value("username", "").toString();
    m_password = params.value("password", "").toString();
    m_privateKeyFile = params.value("private_key", "").toString();

    QString authMethodStr = params.value("auth_method", "password").toString().toLower();
    if (authMethodStr == "publickey" || authMethodStr == "key")
    {
        m_authMethod = PublicKey;
    }
    else if (authMethodStr == "keyboard")
    {
        m_authMethod = KeyboardInteractive;
    }
    else
    {
        m_authMethod = Password;
    }

    // 验证必要参数
    if (m_host.isEmpty() || m_username.isEmpty())
    {
        emit errorOccurred("主机地址和用户名不能为空");
        return false;
    }

    if (m_authMethod == Password && m_password.isEmpty())
    {
        emit errorOccurred("密码认证需要提供密码");
        return false;
    }

    if (m_authMethod == PublicKey && m_privateKeyFile.isEmpty())
    {
        emit errorOccurred("公钥认证需要提供私钥文件");
        return false;
    }

    // 开始连接
    setState(Connecting);

    m_connectionTimer->start(m_connectionTimeout * 1000);
    m_socket->connectToHost(m_host, m_port);

    return true;
}

void SSHConnection::disconnect()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isConnected && m_state == Disconnected)
    {
        return;
    }

    cleanup();

    if (m_socket && m_socket->state() != QAbstractSocket::UnconnectedState)
    {
        m_socket->disconnectFromHost();
        if (m_socket->state() != QAbstractSocket::UnconnectedState)
        {
            m_socket->waitForDisconnected(3000);
        }
    }

    setState(Disconnected);
    m_isConnected = false;
    m_protocolInitialized = false;
    m_authenticated = false;

    emit disconnected();
}

bool SSHConnection::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_isConnected && m_authenticated;
}

SSHConnection::ConnectionState SSHConnection::state() const
{
    QMutexLocker locker(&m_mutex);
    return m_state;
}

QString SSHConnection::stateString() const
{
    switch (state())
    {
    case Disconnected:
        return "未连接";
    case Connecting:
        return "连接中";
    case Authenticating:
        return "认证中";
    case Connected:
        return "已连接";
    case Error:
        return "错误";
    default:
        return "未知状态";
    }
}

bool SSHConnection::sendCommand(const QString &command)
{
    if (!isConnected())
    {
        return false;
    }

    QByteArray data = command.toUtf8() + "\n";
    return sendData(data);
}

bool SSHConnection::sendData(const QByteArray &data)
{
    QMutexLocker locker(&m_mutex);

    if (!m_isConnected || !m_socket || m_socket->state() != QAbstractSocket::ConnectedState)
    {
        return false;
    }

    // 简化的SSH数据发送（实际应该加密和封装）
    qint64 written = m_socket->write(data);
    if (written > 0)
    {
        m_bytesSent += written;
        m_commandCount++;
        return true;
    }

    return false;
}

void SSHConnection::setHost(const QString &host)
{
    QMutexLocker locker(&m_mutex);
    m_host = host;
}

QString SSHConnection::host() const
{
    QMutexLocker locker(&m_mutex);
    return m_host;
}

void SSHConnection::setPort(int port)
{
    QMutexLocker locker(&m_mutex);
    m_port = qBound(1, port, 65535);
}

int SSHConnection::port() const
{
    QMutexLocker locker(&m_mutex);
    return m_port;
}

void SSHConnection::setUsername(const QString &username)
{
    QMutexLocker locker(&m_mutex);
    m_username = username;
}

QString SSHConnection::username() const
{
    QMutexLocker locker(&m_mutex);
    return m_username;
}

void SSHConnection::setPassword(const QString &password)
{
    QMutexLocker locker(&m_mutex);
    m_password = password;
}

void SSHConnection::setPrivateKeyFile(const QString &keyFile)
{
    QMutexLocker locker(&m_mutex);
    m_privateKeyFile = keyFile;
}

QString SSHConnection::privateKeyFile() const
{
    QMutexLocker locker(&m_mutex);
    return m_privateKeyFile;
}

void SSHConnection::setAuthMethod(AuthMethod method)
{
    QMutexLocker locker(&m_mutex);
    m_authMethod = method;
}

SSHConnection::AuthMethod SSHConnection::authMethod() const
{
    QMutexLocker locker(&m_mutex);
    return m_authMethod;
}

void SSHConnection::setConnectionTimeout(int seconds)
{
    m_connectionTimeout = qMax(1, seconds);
}

int SSHConnection::connectionTimeout() const
{
    return m_connectionTimeout;
}

void SSHConnection::setKeepAlive(bool enabled)
{
    m_keepAliveEnabled = enabled;
    setupKeepAlive();
}

bool SSHConnection::keepAliveEnabled() const
{
    return m_keepAliveEnabled;
}

void SSHConnection::setKeepAliveInterval(int seconds)
{
    m_keepAliveInterval = qMax(10, seconds);
    if (m_keepAliveTimer->isActive())
    {
        m_keepAliveTimer->setInterval(m_keepAliveInterval * 1000);
    }
}

int SSHConnection::keepAliveInterval() const
{
    return m_keepAliveInterval;
}

void SSHConnection::setTerminalType(const QString &termType)
{
    QMutexLocker locker(&m_mutex);
    m_terminalType = termType;
}

QString SSHConnection::terminalType() const
{
    QMutexLocker locker(&m_mutex);
    return m_terminalType;
}

void SSHConnection::setTerminalSize(int width, int height)
{
    QMutexLocker locker(&m_mutex);
    m_terminalSize = QSize(qMax(1, width), qMax(1, height));
}

QSize SSHConnection::terminalSize() const
{
    QMutexLocker locker(&m_mutex);
    return m_terminalSize;
}

qint64 SSHConnection::bytesReceived() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesReceived;
}

qint64 SSHConnection::bytesSent() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesSent;
}

QDateTime SSHConnection::connectionTime() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionTime;
}

int SSHConnection::commandCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_commandCount;
}

void SSHConnection::reconnect()
{
    disconnect();

    // 重新连接使用之前的参数
    QVariantMap params;
    params["host"] = m_host;
    params["port"] = m_port;
    params["username"] = m_username;
    params["password"] = m_password;
    params["private_key"] = m_privateKeyFile;

    QString authMethod;
    switch (m_authMethod)
    {
    case PublicKey:
        authMethod = "publickey";
        break;
    case KeyboardInteractive:
        authMethod = "keyboard";
        break;
    default:
        authMethod = "password";
        break;
    }
    params["auth_method"] = authMethod;

    connect(params);
}

void SSHConnection::sendKeepAlive()
{
    if (isConnected())
    {
        // 发送SSH keep-alive包（简化实现）
        sendData(QByteArray("\0", 1));
    }
}

void SSHConnection::onSocketConnected()
{
    QMutexLocker locker(&m_mutex);

    m_connectionTimer->stop();
    m_connectionTime = QDateTime::currentDateTime();

    // 开始SSH协议握手（简化实现）
    setState(Authenticating);

    // 模拟SSH协议初始化
    QTimer::singleShot(100, this, [this]()
                       {
        if (m_authMethod == Password) {
            if (authenticatePassword()) {
                setState(Connected);
                m_isConnected = true;
                m_authenticated = true;
                setupKeepAlive();
                emit connected();
            } else {
                setState(Error);
                emit errorOccurred("密码认证失败");
            }
        } else if (m_authMethod == PublicKey) {
            if (authenticatePublicKey()) {
                setState(Connected);
                m_isConnected = true;
                m_authenticated = true;
                setupKeepAlive();
                emit connected();
            } else {
                setState(Error);
                emit errorOccurred("公钥认证失败");
            }
        } else {
            setState(Error);
            emit errorOccurred("不支持的认证方法");
        } });
}

void SSHConnection::onSocketDisconnected()
{
    QMutexLocker locker(&m_mutex);

    cleanup();
    setState(Disconnected);
    m_isConnected = false;
    m_authenticated = false;

    emit disconnected();
}

void SSHConnection::onSocketError(QAbstractSocket::SocketError error)
{
    QMutexLocker locker(&m_mutex);

    QString errorString;
    switch (error)
    {
    case QAbstractSocket::ConnectionRefusedError:
        errorString = "连接被拒绝";
        break;
    case QAbstractSocket::RemoteHostClosedError:
        errorString = "远程主机关闭连接";
        break;
    case QAbstractSocket::HostNotFoundError:
        errorString = "主机未找到";
        break;
    case QAbstractSocket::SocketTimeoutError:
        errorString = "连接超时";
        break;
    case QAbstractSocket::NetworkError:
        errorString = "网络错误";
        break;
    case QAbstractSocket::SocketAccessError:
        errorString = "套接字访问错误";
        break;
    default:
        errorString = QString("套接字错误: %1").arg(m_socket->errorString());
        break;
    }

    setState(Error);
    emit errorOccurred(formatError(errorString));
}

void SSHConnection::onSocketReadyRead()
{
    QMutexLocker locker(&m_mutex);

    QByteArray data = m_socket->readAll();
    if (!data.isEmpty())
    {
        m_bytesReceived += data.size();
        m_receiveBuffer.append(data);
        processReceivedData();
    }
}

void SSHConnection::onConnectionTimeout()
{
    QMutexLocker locker(&m_mutex);

    if (m_state == Connecting || m_state == Authenticating)
    {
        setState(Error);
        emit errorOccurred("连接超时");
        disconnect();
    }
}

void SSHConnection::onKeepAliveTimer()
{
    sendKeepAlive();
}

void SSHConnection::setState(ConnectionState state)
{
    if (m_state != state)
    {
        m_state = state;
        emit stateChanged(state);
    }
}

void SSHConnection::processReceivedData()
{
    // 简化的数据处理（实际SSH需要解密和解析协议）
    while (!m_receiveBuffer.isEmpty())
    {
        // 查找完整的行
        int newlineIndex = m_receiveBuffer.indexOf('\n');
        if (newlineIndex == -1)
        {
            break;
        }

        QByteArray line = m_receiveBuffer.left(newlineIndex);
        m_receiveBuffer.remove(0, newlineIndex + 1);

        if (!line.isEmpty())
        {
            emit dataReceived(line);
        }
    }

    // 如果缓冲区太大，清理一部分
    if (m_receiveBuffer.size() > 10240)
    {                                                  // 10KB
        m_receiveBuffer = m_receiveBuffer.right(5120); // 保留后5KB
    }
}

bool SSHConnection::authenticatePassword()
{
    // 简化的密码认证（实际需要SSH协议交互）
    if (m_username.isEmpty() || m_password.isEmpty())
    {
        return false;
    }

    // 模拟认证过程
    return !m_password.isEmpty();
}

bool SSHConnection::authenticatePublicKey()
{
    // 简化的公钥认证（实际需要加载和验证密钥）
    if (m_username.isEmpty() || m_privateKeyFile.isEmpty())
    {
        return false;
    }

    return loadPrivateKey(m_privateKeyFile);
}

bool SSHConnection::loadPrivateKey(const QString &keyFile)
{
    QFile file(keyFile);
    if (!file.open(QIODevice::ReadOnly))
    {
        return false;
    }

    QByteArray keyData = file.readAll();
    file.close();

    // 简化的密钥验证（实际需要解析PEM格式等）
    return !keyData.isEmpty() && keyData.contains("PRIVATE KEY");
}

void SSHConnection::setupKeepAlive()
{
    if (m_keepAliveEnabled && m_isConnected)
    {
        m_keepAliveTimer->start(m_keepAliveInterval * 1000);
    }
    else
    {
        m_keepAliveTimer->stop();
    }
}

void SSHConnection::cleanup()
{
    m_connectionTimer->stop();
    m_keepAliveTimer->stop();
    m_receiveBuffer.clear();
    m_sendQueue.clear();
}

QString SSHConnection::formatError(const QString &error) const
{
    return QString("SSH连接错误 [%1:%2]: %3").arg(m_host).arg(m_port).arg(error);
}
