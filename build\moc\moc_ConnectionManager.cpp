/****************************************************************************
** Meta object code from reading C++ file 'ConnectionManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../ConnectionManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConnectionManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ConnectionManager_t {
    QByteArrayData data[34];
    char stringdata0[374];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ConnectionManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ConnectionManager_t qt_meta_stringdata_ConnectionManager = {
    {
QT_MOC_LITERAL(0, 0, 17), // "ConnectionManager"
QT_MOC_LITERAL(1, 18, 13), // "statusChanged"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 6), // "status"
QT_MOC_LITERAL(4, 40, 7), // "details"
QT_MOC_LITERAL(5, 48, 12), // "dataReceived"
QT_MOC_LITERAL(6, 61, 4), // "data"
QT_MOC_LITERAL(7, 66, 13), // "errorOccurred"
QT_MOC_LITERAL(8, 80, 5), // "error"
QT_MOC_LITERAL(9, 86, 21), // "connectionEstablished"
QT_MOC_LITERAL(10, 108, 14), // "connectionLost"
QT_MOC_LITERAL(11, 123, 10), // "testResult"
QT_MOC_LITERAL(12, 134, 7), // "success"
QT_MOC_LITERAL(13, 142, 7), // "message"
QT_MOC_LITERAL(14, 150, 9), // "reconnect"
QT_MOC_LITERAL(15, 160, 14), // "testConnection"
QT_MOC_LITERAL(16, 175, 6), // "params"
QT_MOC_LITERAL(17, 182, 25), // "onConnectionStatusChanged"
QT_MOC_LITERAL(18, 208, 14), // "onDataReceived"
QT_MOC_LITERAL(19, 223, 15), // "onErrorOccurred"
QT_MOC_LITERAL(20, 239, 16), // "onReconnectTimer"
QT_MOC_LITERAL(21, 256, 14), // "ConnectionType"
QT_MOC_LITERAL(22, 271, 4), // "None"
QT_MOC_LITERAL(23, 276, 6), // "Serial"
QT_MOC_LITERAL(24, 283, 3), // "SSH"
QT_MOC_LITERAL(25, 287, 3), // "ADB"
QT_MOC_LITERAL(26, 291, 7), // "Network"
QT_MOC_LITERAL(27, 299, 3), // "FTP"
QT_MOC_LITERAL(28, 303, 16), // "ConnectionStatus"
QT_MOC_LITERAL(29, 320, 12), // "Disconnected"
QT_MOC_LITERAL(30, 333, 10), // "Connecting"
QT_MOC_LITERAL(31, 344, 9), // "Connected"
QT_MOC_LITERAL(32, 354, 13), // "Disconnecting"
QT_MOC_LITERAL(33, 368, 5) // "Error"

    },
    "ConnectionManager\0statusChanged\0\0"
    "status\0details\0dataReceived\0data\0"
    "errorOccurred\0error\0connectionEstablished\0"
    "connectionLost\0testResult\0success\0"
    "message\0reconnect\0testConnection\0"
    "params\0onConnectionStatusChanged\0"
    "onDataReceived\0onErrorOccurred\0"
    "onReconnectTimer\0ConnectionType\0None\0"
    "Serial\0SSH\0ADB\0Network\0FTP\0ConnectionStatus\0"
    "Disconnected\0Connecting\0Connected\0"
    "Disconnecting\0Error"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ConnectionManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       2,  104, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   74,    2, 0x06 /* Public */,
       5,    1,   79,    2, 0x06 /* Public */,
       7,    1,   82,    2, 0x06 /* Public */,
       9,    0,   85,    2, 0x06 /* Public */,
      10,    0,   86,    2, 0x06 /* Public */,
      11,    2,   87,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      14,    0,   92,    2, 0x0a /* Public */,
      15,    1,   93,    2, 0x0a /* Public */,
      17,    0,   96,    2, 0x08 /* Private */,
      18,    1,   97,    2, 0x08 /* Private */,
      19,    1,  100,    2, 0x08 /* Private */,
      20,    0,  103,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   12,   13,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::QVariantMap,   16,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QByteArray,    6,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void,

 // enums: name, alias, flags, count, data
      21,   21, 0x0,    6,  114,
      28,   28, 0x0,    5,  126,

 // enum data: key, value
      22, uint(ConnectionManager::None),
      23, uint(ConnectionManager::Serial),
      24, uint(ConnectionManager::SSH),
      25, uint(ConnectionManager::ADB),
      26, uint(ConnectionManager::Network),
      27, uint(ConnectionManager::FTP),
      29, uint(ConnectionManager::Disconnected),
      30, uint(ConnectionManager::Connecting),
      31, uint(ConnectionManager::Connected),
      32, uint(ConnectionManager::Disconnecting),
      33, uint(ConnectionManager::Error),

       0        // eod
};

void ConnectionManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConnectionManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->statusChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 1: _t->dataReceived((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->connectionEstablished(); break;
        case 4: _t->connectionLost(); break;
        case 5: _t->testResult((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 6: _t->reconnect(); break;
        case 7: _t->testConnection((*reinterpret_cast< const QVariantMap(*)>(_a[1]))); break;
        case 8: _t->onConnectionStatusChanged(); break;
        case 9: _t->onDataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 10: _t->onErrorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 11: _t->onReconnectTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ConnectionManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::statusChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::dataReceived)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::errorOccurred)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::connectionEstablished)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::connectionLost)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(bool , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::testResult)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ConnectionManager::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_ConnectionManager.data,
    qt_meta_data_ConnectionManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ConnectionManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConnectionManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ConnectionManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ConnectionManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void ConnectionManager::statusChanged(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ConnectionManager::dataReceived(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ConnectionManager::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ConnectionManager::connectionEstablished()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ConnectionManager::connectionLost()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ConnectionManager::testResult(bool _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
