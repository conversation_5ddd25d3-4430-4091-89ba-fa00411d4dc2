/****************************************************************************
** Meta object code from reading C++ file 'ADBConnection.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../ADBConnection.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ADBConnection.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ADBConnection_t {
    QByteArrayData data[25];
    char stringdata0[335];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ADBConnection_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ADBConnection_t qt_meta_stringdata_ADBConnection = {
    {
QT_MOC_LITERAL(0, 0, 13), // "ADBConnection"
QT_MOC_LITERAL(1, 14, 9), // "connected"
QT_MOC_LITERAL(2, 24, 0), // ""
QT_MOC_LITERAL(3, 25, 12), // "disconnected"
QT_MOC_LITERAL(4, 38, 12), // "dataReceived"
QT_MOC_LITERAL(5, 51, 4), // "data"
QT_MOC_LITERAL(6, 56, 13), // "errorOccurred"
QT_MOC_LITERAL(7, 70, 5), // "error"
QT_MOC_LITERAL(8, 76, 12), // "stateChanged"
QT_MOC_LITERAL(9, 89, 15), // "ConnectionState"
QT_MOC_LITERAL(10, 105, 5), // "state"
QT_MOC_LITERAL(11, 111, 17), // "deviceListChanged"
QT_MOC_LITERAL(12, 129, 15), // "commandFinished"
QT_MOC_LITERAL(13, 145, 8), // "exitCode"
QT_MOC_LITERAL(14, 154, 6), // "output"
QT_MOC_LITERAL(15, 161, 9), // "reconnect"
QT_MOC_LITERAL(16, 171, 17), // "refreshDeviceInfo"
QT_MOC_LITERAL(17, 189, 17), // "onProcessFinished"
QT_MOC_LITERAL(18, 207, 20), // "QProcess::ExitStatus"
QT_MOC_LITERAL(19, 228, 10), // "exitStatus"
QT_MOC_LITERAL(20, 239, 14), // "onProcessError"
QT_MOC_LITERAL(21, 254, 22), // "QProcess::ProcessError"
QT_MOC_LITERAL(22, 277, 18), // "onProcessReadyRead"
QT_MOC_LITERAL(23, 296, 19), // "onConnectionTimeout"
QT_MOC_LITERAL(24, 316, 18) // "onDeviceCheckTimer"

    },
    "ADBConnection\0connected\0\0disconnected\0"
    "dataReceived\0data\0errorOccurred\0error\0"
    "stateChanged\0ConnectionState\0state\0"
    "deviceListChanged\0commandFinished\0"
    "exitCode\0output\0reconnect\0refreshDeviceInfo\0"
    "onProcessFinished\0QProcess::ExitStatus\0"
    "exitStatus\0onProcessError\0"
    "QProcess::ProcessError\0onProcessReadyRead\0"
    "onConnectionTimeout\0onDeviceCheckTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ADBConnection[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   84,    2, 0x06 /* Public */,
       3,    0,   85,    2, 0x06 /* Public */,
       4,    1,   86,    2, 0x06 /* Public */,
       6,    1,   89,    2, 0x06 /* Public */,
       8,    1,   92,    2, 0x06 /* Public */,
      11,    0,   95,    2, 0x06 /* Public */,
      12,    2,   96,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      15,    0,  101,    2, 0x0a /* Public */,
      16,    0,  102,    2, 0x0a /* Public */,
      17,    2,  103,    2, 0x08 /* Private */,
      20,    1,  108,    2, 0x08 /* Private */,
      22,    0,  111,    2, 0x08 /* Private */,
      23,    0,  112,    2, 0x08 /* Private */,
      24,    0,  113,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QByteArray,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,   13,   14,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, 0x80000000 | 18,   13,   19,
    QMetaType::Void, 0x80000000 | 21,    7,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ADBConnection::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ADBConnection *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->connected(); break;
        case 1: _t->disconnected(); break;
        case 2: _t->dataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 3: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->stateChanged((*reinterpret_cast< ConnectionState(*)>(_a[1]))); break;
        case 5: _t->deviceListChanged(); break;
        case 6: _t->commandFinished((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 7: _t->reconnect(); break;
        case 8: _t->refreshDeviceInfo(); break;
        case 9: _t->onProcessFinished((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< QProcess::ExitStatus(*)>(_a[2]))); break;
        case 10: _t->onProcessError((*reinterpret_cast< QProcess::ProcessError(*)>(_a[1]))); break;
        case 11: _t->onProcessReadyRead(); break;
        case 12: _t->onConnectionTimeout(); break;
        case 13: _t->onDeviceCheckTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ADBConnection::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ADBConnection::connected)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ADBConnection::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ADBConnection::disconnected)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ADBConnection::*)(const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ADBConnection::dataReceived)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ADBConnection::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ADBConnection::errorOccurred)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ADBConnection::*)(ConnectionState );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ADBConnection::stateChanged)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ADBConnection::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ADBConnection::deviceListChanged)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ADBConnection::*)(int , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ADBConnection::commandFinished)) {
                *result = 6;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ADBConnection::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_ADBConnection.data,
    qt_meta_data_ADBConnection,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ADBConnection::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ADBConnection::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ADBConnection.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ADBConnection::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 14;
    }
    return _id;
}

// SIGNAL 0
void ADBConnection::connected()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ADBConnection::disconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ADBConnection::dataReceived(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ADBConnection::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ADBConnection::stateChanged(ConnectionState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ADBConnection::deviceListChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ADBConnection::commandFinished(int _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
