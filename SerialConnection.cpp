#include "SerialConnection.h"
#include <QDebug>
#include <QThread>

SerialConnection::SerialConnection(QObject *parent)
    : QObject(parent)
    , m_serialPort(nullptr)
    , m_baudRate(115200)
    , m_dataBits(8)
    , m_stopBits(1.0)
    , m_parity("None")
    , m_flowControl("None")
    , m_autoLogin(false)
    , m_loginState(NotLoggedIn)
    , m_loginTimer(nullptr)
    , m_readTimeout(3000)
    , m_writeTimeout(3000)
{
    // 初始化串口对象
    m_serialPort = new QSerialPort(this);
    
    // 连接信号
    connect(m_serialPort, &QSerialPort::readyRead,
            this, &SerialConnection::onReadyRead);
    connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::errorOccurred),
            this, &SerialConnection::onErrorOccurred);
    
    // 初始化登录定时器
    m_loginTimer = new QTimer(this);
    m_loginTimer->setSingleShot(true);
    m_loginTimer->setInterval(10000); // 10秒超时
    connect(m_loginTimer, &QTimer::timeout, this, &SerialConnection::onLoginTimeout);
    
    // 初始化登录提示符
    m_loginPrompts << "login:" << "username:" << "user:";
    m_passwordPrompts << "password:" << "passwd:";
    m_commandPrompts << "# " << "$ " << "> " << "~$ " << "~# ";
}

SerialConnection::~SerialConnection()
{
    disconnect();
}

bool SerialConnection::connect(const QVariantMap &params)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_serialPort->isOpen()) {
        disconnect();
    }
    
    // 解析连接参数
    m_portName = params.value("port").toString();
    m_baudRate = params.value("baud_rate", 115200).toInt();
    m_dataBits = params.value("data_bits", 8).toInt();
    m_stopBits = params.value("stop_bits", 1.0).toFloat();
    m_parity = params.value("parity", "None").toString();
    m_flowControl = params.value("flow_control", "None").toString();
    
    // 登录参数
    m_username = params.value("username").toString();
    m_password = params.value("password").toString();
    m_autoLogin = !m_username.isEmpty() && !m_password.isEmpty();
    
    // 配置串口
    m_serialPort->setPortName(m_portName);
    m_serialPort->setBaudRate(intToBaudRate(m_baudRate));
    m_serialPort->setDataBits(intToDataBits(m_dataBits));
    m_serialPort->setStopBits(floatToStopBits(m_stopBits));
    m_serialPort->setParity(stringToParity(m_parity));
    m_serialPort->setFlowControl(stringToFlowControl(m_flowControl));
    
    // 尝试打开串口
    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        emit errorOccurred(QString("无法打开串口 %1: %2")
                          .arg(m_portName)
                          .arg(m_serialPort->errorString()));
        return false;
    }
    
    // 清空缓冲区
    m_dataBuffer.clear();
    m_lineBuffer.clear();
    m_loginState = NotLoggedIn;
    
    emit connected();
    
    // 如果启用自动登录，发送回车获取提示符
    if (m_autoLogin) {
        QThread::msleep(500); // 等待设备稳定
        sendData("\r\n");
        m_loginTimer->start();
    }
    
    return true;
}

void SerialConnection::disconnect()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->close();
        emit disconnected();
    }
    
    if (m_loginTimer) {
        m_loginTimer->stop();
    }
    
    m_loginState = NotLoggedIn;
}

bool SerialConnection::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_serialPort && m_serialPort->isOpen();
}

bool SerialConnection::sendData(const QByteArray &data)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_serialPort || !m_serialPort->isOpen()) {
        return false;
    }
    
    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1) {
        emit errorOccurred("数据发送失败: " + m_serialPort->errorString());
        return false;
    }
    
    // 确保数据立即发送
    if (!m_serialPort->flush()) {
        qWarning() << "串口数据刷新失败";
    }
    
    return bytesWritten == data.size();
}

bool SerialConnection::sendCommand(const QString &command)
{
    QString cmd = command;
    if (!cmd.endsWith("\r\n") && !cmd.endsWith("\n")) {
        cmd += "\r\n";
    }
    
    return sendData(cmd.toUtf8());
}

void SerialConnection::onReadyRead()
{
    QByteArray data = m_serialPort->readAll();
    m_dataBuffer.append(data);
    
    processReceivedData();
}

void SerialConnection::processReceivedData()
{
    // 处理完整的行
    while (m_dataBuffer.contains('\n')) {
        int index = m_dataBuffer.indexOf('\n');
        QString line = QString::fromUtf8(m_dataBuffer.left(index)).trimmed();
        m_dataBuffer.remove(0, index + 1);
        
        if (!line.isEmpty()) {
            emit dataReceived(line.toUtf8());
            
            // 处理登录流程
            if (m_autoLogin) {
                handleLoginFlow(line);
            }
        }
    }
    
    // 检查是否有未完成的提示符（不以换行结束）
    if (!m_dataBuffer.isEmpty()) {
        QString bufferStr = QString::fromUtf8(m_dataBuffer);
        
        // 检查登录提示符
        if (isLoginPrompt(bufferStr)) {
            emit dataReceived(m_dataBuffer);
            if (m_autoLogin) {
                handleLoginFlow(bufferStr);
            }
            m_dataBuffer.clear();
        }
        // 检查命令提示符
        else if (isCommandPrompt(bufferStr)) {
            emit dataReceived(m_dataBuffer);
            if (m_loginState != LoggedIn) {
                m_loginState = LoggedIn;
                m_loginTimer->stop();
                emit loginCompleted(true);
            }
            m_dataBuffer.clear();
        }
    }
}

bool SerialConnection::isLoginPrompt(const QString &text) const
{
    QString lowerText = text.toLower();
    for (const QString &prompt : m_loginPrompts) {
        if (lowerText.contains(prompt)) {
            return true;
        }
    }
    for (const QString &prompt : m_passwordPrompts) {
        if (lowerText.contains(prompt)) {
            return true;
        }
    }
    return false;
}

bool SerialConnection::isCommandPrompt(const QString &text) const
{
    for (const QString &prompt : m_commandPrompts) {
        if (text.endsWith(prompt)) {
            return true;
        }
    }
    return false;
}

void SerialConnection::handleLoginFlow(const QString &data)
{
    QString lowerData = data.toLower();
    
    // 检查是否是用户名提示
    bool isUsernamePrompt = false;
    for (const QString &prompt : m_loginPrompts) {
        if (lowerData.contains(prompt)) {
            isUsernamePrompt = true;
            break;
        }
    }
    
    // 检查是否是密码提示
    bool isPasswordPrompt = false;
    for (const QString &prompt : m_passwordPrompts) {
        if (lowerData.contains(prompt)) {
            isPasswordPrompt = true;
            break;
        }
    }
    
    // 处理登录状态机
    if (isUsernamePrompt && m_loginState == NotLoggedIn) {
        m_loginState = WaitingForLogin;
        emit loginPromptDetected("用户名提示");
        
        // 发送用户名
        QThread::msleep(200);
        sendCommand(m_username);
    }
    else if (isPasswordPrompt && (m_loginState == WaitingForLogin || m_loginState == NotLoggedIn)) {
        m_loginState = WaitingForPassword;
        emit loginPromptDetected("密码提示");
        
        // 发送密码
        QThread::msleep(200);
        sendCommand(m_password);
    }
    else if (lowerData.contains("incorrect") || lowerData.contains("failed") || lowerData.contains("denied")) {
        m_loginState = LoginFailed;
        m_loginTimer->stop();
        emit loginCompleted(false);
        emit errorOccurred("登录失败: 用户名或密码错误");
    }
    else if (isCommandPrompt(data)) {
        m_loginState = LoggedIn;
        m_loginTimer->stop();
        emit loginCompleted(true);
    }
}

void SerialConnection::onErrorOccurred(QSerialPort::SerialPortError error)
{
    if (error != QSerialPort::NoError) {
        QString errorString;
        switch (error) {
        case QSerialPort::DeviceNotFoundError:
            errorString = "设备未找到";
            break;
        case QSerialPort::PermissionError:
            errorString = "权限错误";
            break;
        case QSerialPort::OpenError:
            errorString = "打开失败";
            break;
        case QSerialPort::WriteError:
            errorString = "写入错误";
            break;
        case QSerialPort::ReadError:
            errorString = "读取错误";
            break;
        case QSerialPort::ResourceError:
            errorString = "资源错误";
            break;
        case QSerialPort::UnsupportedOperationError:
            errorString = "不支持的操作";
            break;
        case QSerialPort::TimeoutError:
            errorString = "超时错误";
            break;
        default:
            errorString = "未知错误";
            break;
        }
        
        emit errorOccurred(QString("串口错误: %1").arg(errorString));
    }
}

void SerialConnection::onLoginTimeout()
{
    if (m_loginState != LoggedIn) {
        m_loginState = LoginFailed;
        emit loginCompleted(false);
        emit errorOccurred("登录超时");
    }
}

QStringList SerialConnection::getAvailablePorts()
{
    QStringList ports;
    const auto portInfos = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &portInfo : portInfos) {
        QString portDescription = QString("%1 - %2")
                                 .arg(portInfo.portName())
                                 .arg(portInfo.description());
        ports << portDescription;
    }
    return ports;
}

// 辅助转换函数
QSerialPort::BaudRate SerialConnection::intToBaudRate(int baudRate) const
{
    switch (baudRate) {
    case 1200: return QSerialPort::Baud1200;
    case 2400: return QSerialPort::Baud2400;
    case 4800: return QSerialPort::Baud4800;
    case 9600: return QSerialPort::Baud9600;
    case 19200: return QSerialPort::Baud19200;
    case 38400: return QSerialPort::Baud38400;
    case 57600: return QSerialPort::Baud57600;
    case 115200: return QSerialPort::Baud115200;
    default:
        // 对于自定义波特率，使用setBaudRate(int)方法
        return static_cast<QSerialPort::BaudRate>(baudRate);
    }
}

QSerialPort::DataBits SerialConnection::intToDataBits(int dataBits) const
{
    switch (dataBits) {
    case 5: return QSerialPort::Data5;
    case 6: return QSerialPort::Data6;
    case 7: return QSerialPort::Data7;
    case 8: return QSerialPort::Data8;
    default: return QSerialPort::Data8;
    }
}

QSerialPort::StopBits SerialConnection::floatToStopBits(float stopBits) const
{
    if (stopBits == 1.0) return QSerialPort::OneStop;
    else if (stopBits == 1.5) return QSerialPort::OneAndHalfStop;
    else if (stopBits == 2.0) return QSerialPort::TwoStop;
    else return QSerialPort::OneStop;
}

QSerialPort::Parity SerialConnection::stringToParity(const QString &parity) const
{
    QString p = parity.toLower();
    if (p == "even") return QSerialPort::EvenParity;
    else if (p == "odd") return QSerialPort::OddParity;
    else if (p == "mark") return QSerialPort::MarkParity;
    else if (p == "space") return QSerialPort::SpaceParity;
    else return QSerialPort::NoParity;
}

QSerialPort::FlowControl SerialConnection::stringToFlowControl(const QString &flowControl) const
{
    QString fc = flowControl.toLower();
    if (fc == "rts/cts") return QSerialPort::HardwareControl;
    else if (fc == "xon/xoff") return QSerialPort::SoftwareControl;
    else return QSerialPort::NoFlowControl;
}
