/****************************************************************************
** Meta object code from reading C++ file 'CommandHistory.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../CommandHistory.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CommandHistory.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CommandHistory_t {
    QByteArrayData data[10];
    char stringdata0[105];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CommandHistory_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CommandHistory_t qt_meta_stringdata_CommandHistory = {
    {
QT_MOC_LITERAL(0, 0, 14), // "CommandHistory"
QT_MOC_LITERAL(1, 15, 12), // "commandAdded"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 7), // "command"
QT_MOC_LITERAL(4, 37, 14), // "historyCleared"
QT_MOC_LITERAL(5, 52, 12), // "historySaved"
QT_MOC_LITERAL(6, 65, 13), // "historyLoaded"
QT_MOC_LITERAL(7, 79, 4), // "save"
QT_MOC_LITERAL(8, 84, 4), // "load"
QT_MOC_LITERAL(9, 89, 15) // "onAutoSaveTimer"

    },
    "CommandHistory\0commandAdded\0\0command\0"
    "historyCleared\0historySaved\0historyLoaded\0"
    "save\0load\0onAutoSaveTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CommandHistory[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   49,    2, 0x06 /* Public */,
       4,    0,   52,    2, 0x06 /* Public */,
       5,    0,   53,    2, 0x06 /* Public */,
       6,    0,   54,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    0,   55,    2, 0x0a /* Public */,
       8,    0,   56,    2, 0x0a /* Public */,
       9,    0,   57,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void CommandHistory::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CommandHistory *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->commandAdded((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->historyCleared(); break;
        case 2: _t->historySaved(); break;
        case 3: _t->historyLoaded(); break;
        case 4: _t->save(); break;
        case 5: _t->load(); break;
        case 6: _t->onAutoSaveTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CommandHistory::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandHistory::commandAdded)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CommandHistory::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandHistory::historyCleared)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CommandHistory::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandHistory::historySaved)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (CommandHistory::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandHistory::historyLoaded)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CommandHistory::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_CommandHistory.data,
    qt_meta_data_CommandHistory,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CommandHistory::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CommandHistory::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CommandHistory.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int CommandHistory::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void CommandHistory::commandAdded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CommandHistory::historyCleared()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void CommandHistory::historySaved()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void CommandHistory::historyLoaded()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
