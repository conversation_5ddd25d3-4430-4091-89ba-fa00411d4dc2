# RF调试工具 Qt版本

一个功能强大的RF调试工具，基于Qt框架开发，支持多种连接方式和现代化的用户界面。

## 功能特性

### 🔌 多种连接方式
- **串口连接**: 支持高波特率 (9600-3,000,000)，完整的串口参数配置
- **SSH连接**: 支持密钥和密码认证，安全的远程连接
- **ADB连接**: Android设备调试，支持多设备管理
- **网络连接**: TCP/UDP连接支持
- **FTP连接**: 文件传输协议支持

### 💻 智能终端
- 交互式终端界面，支持语法高亮
- 时间戳显示，可自定义格式
- 自动滚动和手动导航
- 支持复制、粘贴、查找功能
- 可调节字体大小和颜色主题

### 📝 命令管理
- 常用命令列表，支持分类管理
- 命令历史记录，智能搜索
- 快捷命令按钮，一键执行
- 命令导入导出功能

### 🎨 现代化界面
- Mac风格设计，美观易用
- 可自定义背景（纯色、渐变、图片）
- 响应式布局，支持窗口缩放
- 暗色/亮色主题切换

### 📊 高级日志
- 多级别日志系统（调试、信息、警告、错误）
- 日志文件自动轮转
- 时间戳和格式化输出
- 日志过滤和搜索

### ⚙️ 配置管理
- 完整的配置系统，支持导入导出
- 连接历史记录
- 窗口状态保存
- 自动保存和备份

## 项目结构

```
src/
├── main.cpp                    # 程序入口
├── MainWindow.h/.cpp          # 主窗口
├── ConnectionManager.h/.cpp   # 连接管理器
├── ConfigManager.h/.cpp       # 配置管理器
├── LogManager.h/.cpp          # 日志管理器
├── InteractiveTerminal.h/.cpp # 交互式终端
├── CommandHistory.h/.cpp      # 命令历史
├── CommandListWidget.h/.cpp   # 命令列表组件
├── SerialConnection.h/.cpp    # 串口连接
├── SSHConnection.h/.cpp       # SSH连接
├── ADBConnection.h/.cpp       # ADB连接
├── ConnectionDialog.h/.cpp    # 连接对话框
├── LogConfigDialog.h/.cpp     # 日志配置对话框
├── BackgroundConfigDialog.h/.cpp # 背景配置对话框
├── CMakeLists.txt             # CMake构建文件
├── RFTool_Qt.pro             # qmake项目文件
└── README.md                  # 项目说明
```

## 编译要求

### 依赖项
- Qt 6.0 或更高版本
- C++17 编译器
- CMake 3.16+ 或 qmake

### Qt模块
- Qt6::Core
- Qt6::Widgets
- Qt6::SerialPort
- Qt6::Network

## 编译方法

### 使用CMake
```bash
mkdir build
cd build
cmake ..
make
```

### 使用qmake
```bash
qmake RFTool_Qt.pro
make
```

### 使用Qt Creator
1. 打开 `RFTool_Qt.pro` 或 `CMakeLists.txt`
2. 配置构建套件
3. 点击构建按钮

## 使用说明

### 快速开始
1. 启动程序
2. 点击"连接设备"按钮
3. 选择连接类型并配置参数
4. 点击"连接"开始调试

### 连接配置
- **串口**: 选择端口、波特率等参数
- **SSH**: 输入主机地址、用户名、密码或密钥
- **ADB**: 选择Android设备
- **网络**: 配置主机和端口
- **FTP**: 设置FTP服务器信息

### 命令操作
- 在命令输入框中输入命令，按Enter发送
- 使用快捷命令按钮快速执行常用命令
- 在命令列表中管理和组织常用命令
- 使用上下箭头键浏览命令历史

### 界面定制
- 通过配置菜单设置背景和主题
- 调整终端字体和颜色
- 配置日志级别和输出格式
- 保存和恢复窗口布局

## 技术特点

### 架构设计
- 模块化设计，松耦合架构
- 基于信号槽的事件驱动
- 多线程安全的数据处理
- 插件式的连接管理

### 性能优化
- 异步I/O操作，不阻塞UI
- 内存池管理，减少内存碎片
- 智能缓存机制
- 高效的数据序列化

### 安全性
- 密码加密存储
- 安全的SSH连接
- 输入验证和过滤
- 错误处理和恢复

## 版本信息

- **版本**: 2.0.0
- **开发语言**: C++17
- **UI框架**: Qt 6
- **许可证**: MIT License

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

### 开发环境设置
1. 安装Qt 6开发环境
2. 克隆项目代码
3. 配置IDE（推荐Qt Creator）
4. 编译和测试

### 代码规范
- 遵循Qt编码规范
- 使用有意义的变量和函数名
- 添加适当的注释
- 编写单元测试

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发团队

---

**RF调试工具 Qt版本** - 让RF调试更简单、更高效！
