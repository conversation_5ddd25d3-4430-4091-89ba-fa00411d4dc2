/****************************************************************************
** Meta object code from reading C++ file 'MainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../MainWindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MainWindow_t {
    QByteArrayData data[44];
    char stringdata0[603];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MainWindow_t qt_meta_stringdata_MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MainWindow"
QT_MOC_LITERAL(1, 11, 20), // "showConnectionDialog"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 13), // "connectDevice"
QT_MOC_LITERAL(4, 47, 16), // "disconnectDevice"
QT_MOC_LITERAL(5, 64, 25), // "onConnectionStatusChanged"
QT_MOC_LITERAL(6, 90, 6), // "status"
QT_MOC_LITERAL(7, 97, 7), // "details"
QT_MOC_LITERAL(8, 105, 14), // "onDataReceived"
QT_MOC_LITERAL(9, 120, 4), // "data"
QT_MOC_LITERAL(10, 125, 15), // "onErrorOccurred"
QT_MOC_LITERAL(11, 141, 5), // "error"
QT_MOC_LITERAL(12, 147, 11), // "sendCommand"
QT_MOC_LITERAL(13, 159, 19), // "executeQuickCommand"
QT_MOC_LITERAL(14, 179, 20), // "executeCommonCommand"
QT_MOC_LITERAL(15, 200, 4), // "name"
QT_MOC_LITERAL(16, 205, 7), // "content"
QT_MOC_LITERAL(17, 213, 15), // "onCommandEdited"
QT_MOC_LITERAL(18, 229, 7), // "oldName"
QT_MOC_LITERAL(19, 237, 7), // "newName"
QT_MOC_LITERAL(20, 245, 10), // "newContent"
QT_MOC_LITERAL(21, 256, 16), // "onCommandDeleted"
QT_MOC_LITERAL(22, 273, 14), // "onCommandAdded"
QT_MOC_LITERAL(23, 288, 13), // "addNewCommand"
QT_MOC_LITERAL(24, 302, 14), // "showConfigMenu"
QT_MOC_LITERAL(25, 317, 13), // "showLogConfig"
QT_MOC_LITERAL(26, 331, 20), // "showBackgroundConfig"
QT_MOC_LITERAL(27, 352, 12), // "showUIConfig"
QT_MOC_LITERAL(28, 365, 18), // "showTerminalConfig"
QT_MOC_LITERAL(29, 384, 11), // "resetConfig"
QT_MOC_LITERAL(30, 396, 15), // "onConfigChanged"
QT_MOC_LITERAL(31, 412, 3), // "key"
QT_MOC_LITERAL(32, 416, 5), // "value"
QT_MOC_LITERAL(33, 422, 20), // "showImportExportMenu"
QT_MOC_LITERAL(34, 443, 12), // "exportConfig"
QT_MOC_LITERAL(35, 456, 12), // "importConfig"
QT_MOC_LITERAL(36, 469, 14), // "exportCommands"
QT_MOC_LITERAL(37, 484, 14), // "importCommands"
QT_MOC_LITERAL(38, 499, 19), // "toggleCommandsPanel"
QT_MOC_LITERAL(39, 519, 22), // "updateConnectionStatus"
QT_MOC_LITERAL(40, 542, 20), // "updateCommandButtons"
QT_MOC_LITERAL(41, 563, 21), // "applyBackgroundConfig"
QT_MOC_LITERAL(42, 585, 8), // "showHelp"
QT_MOC_LITERAL(43, 594, 8) // "autoSave"

    },
    "MainWindow\0showConnectionDialog\0\0"
    "connectDevice\0disconnectDevice\0"
    "onConnectionStatusChanged\0status\0"
    "details\0onDataReceived\0data\0onErrorOccurred\0"
    "error\0sendCommand\0executeQuickCommand\0"
    "executeCommonCommand\0name\0content\0"
    "onCommandEdited\0oldName\0newName\0"
    "newContent\0onCommandDeleted\0onCommandAdded\0"
    "addNewCommand\0showConfigMenu\0showLogConfig\0"
    "showBackgroundConfig\0showUIConfig\0"
    "showTerminalConfig\0resetConfig\0"
    "onConfigChanged\0key\0value\0"
    "showImportExportMenu\0exportConfig\0"
    "importConfig\0exportCommands\0importCommands\0"
    "toggleCommandsPanel\0updateConnectionStatus\0"
    "updateCommandButtons\0applyBackgroundConfig\0"
    "showHelp\0autoSave"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      31,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  169,    2, 0x08 /* Private */,
       3,    0,  170,    2, 0x08 /* Private */,
       4,    0,  171,    2, 0x08 /* Private */,
       5,    2,  172,    2, 0x08 /* Private */,
       8,    1,  177,    2, 0x08 /* Private */,
      10,    1,  180,    2, 0x08 /* Private */,
      12,    0,  183,    2, 0x08 /* Private */,
      13,    0,  184,    2, 0x08 /* Private */,
      14,    2,  185,    2, 0x08 /* Private */,
      17,    3,  190,    2, 0x08 /* Private */,
      21,    1,  197,    2, 0x08 /* Private */,
      22,    2,  200,    2, 0x08 /* Private */,
      23,    0,  205,    2, 0x08 /* Private */,
      24,    0,  206,    2, 0x08 /* Private */,
      25,    0,  207,    2, 0x08 /* Private */,
      26,    0,  208,    2, 0x08 /* Private */,
      27,    0,  209,    2, 0x08 /* Private */,
      28,    0,  210,    2, 0x08 /* Private */,
      29,    0,  211,    2, 0x08 /* Private */,
      30,    2,  212,    2, 0x08 /* Private */,
      33,    0,  217,    2, 0x08 /* Private */,
      34,    0,  218,    2, 0x08 /* Private */,
      35,    0,  219,    2, 0x08 /* Private */,
      36,    0,  220,    2, 0x08 /* Private */,
      37,    0,  221,    2, 0x08 /* Private */,
      38,    0,  222,    2, 0x08 /* Private */,
      39,    0,  223,    2, 0x08 /* Private */,
      40,    0,  224,    2, 0x08 /* Private */,
      41,    0,  225,    2, 0x08 /* Private */,
      42,    0,  226,    2, 0x08 /* Private */,
      43,    0,  227,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    6,    7,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   15,   16,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QString,   18,   19,   20,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   15,   16,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QVariant,   31,   32,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->showConnectionDialog(); break;
        case 1: _t->connectDevice(); break;
        case 2: _t->disconnectDevice(); break;
        case 3: _t->onConnectionStatusChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->onDataReceived((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->onErrorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 6: _t->sendCommand(); break;
        case 7: _t->executeQuickCommand(); break;
        case 8: _t->executeCommonCommand((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 9: _t->onCommandEdited((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 10: _t->onCommandDeleted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 11: _t->onCommandAdded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 12: _t->addNewCommand(); break;
        case 13: _t->showConfigMenu(); break;
        case 14: _t->showLogConfig(); break;
        case 15: _t->showBackgroundConfig(); break;
        case 16: _t->showUIConfig(); break;
        case 17: _t->showTerminalConfig(); break;
        case 18: _t->resetConfig(); break;
        case 19: _t->onConfigChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QVariant(*)>(_a[2]))); break;
        case 20: _t->showImportExportMenu(); break;
        case 21: _t->exportConfig(); break;
        case 22: _t->importConfig(); break;
        case 23: _t->exportCommands(); break;
        case 24: _t->importCommands(); break;
        case 25: _t->toggleCommandsPanel(); break;
        case 26: _t->updateConnectionStatus(); break;
        case 27: _t->updateCommandButtons(); break;
        case 28: _t->applyBackgroundConfig(); break;
        case 29: _t->showHelp(); break;
        case 30: _t->autoSave(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MainWindow::staticMetaObject = { {
    &QMainWindow::staticMetaObject,
    qt_meta_stringdata_MainWindow.data,
    qt_meta_data_MainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 31)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 31;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 31)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 31;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
