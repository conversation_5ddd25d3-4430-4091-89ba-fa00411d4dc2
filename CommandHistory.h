#ifndef COMMANDHISTORY_H
#define COMMANDHISTORY_H

#include <QObject>
#include <QStringList>
#include <QDateTime>
#include <QVariantMap>
#include <QTimer>
#include <QMutex>

class CommandHistory : public QObject
{
    Q_OBJECT

public:
    struct HistoryItem {
        QString command;
        QDateTime timestamp;
        QString result;
        bool success;
        int executionTime; // 毫秒
    };

    explicit CommandHistory(QObject *parent = nullptr);
    ~CommandHistory();

    // 命令历史管理
    void addCommand(const QString &command, const QString &result = QString(), bool success = true, int executionTime = 0);
    void clearHistory();
    QStringList getCommands() const;
    QList<HistoryItem> getHistory() const;
    HistoryItem getHistoryItem(int index) const;
    int historySize() const;

    // 导航功能
    QString getPreviousCommand();
    QString getNextCommand();
    void resetNavigation();
    int currentIndex() const;

    // 搜索功能
    QStringList searchCommands(const QString &pattern) const;
    QList<HistoryItem> searchHistory(const QString &pattern) const;

    // 配置
    void setMaxHistorySize(int size);
    int maxHistorySize() const;
    void setAutoSave(bool enabled);
    bool isAutoSaveEnabled() const;
    void setAutoSaveInterval(int seconds);
    int autoSaveInterval() const;

    // 文件操作
    bool saveToFile(const QString &filePath = QString()) const;
    bool loadFromFile(const QString &filePath = QString());
    void setHistoryFile(const QString &filePath);
    QString historyFile() const;

    // 统计信息
    int getTotalCommands() const;
    QDateTime getFirstCommandTime() const;
    QDateTime getLastCommandTime() const;
    QStringList getMostUsedCommands(int count = 10) const;

public slots:
    void save();
    void load();

signals:
    void commandAdded(const QString &command);
    void historyCleared();
    void historySaved();
    void historyLoaded();

private slots:
    void onAutoSaveTimer();

private:
    void trimHistory();
    void updateNavigationIndex();
    QString getDefaultHistoryFile() const;

private:
    QList<HistoryItem> m_history;
    QStringList m_commands; // 快速访问命令列表
    
    // 导航状态
    int m_navigationIndex;
    bool m_isNavigating;
    
    // 配置
    int m_maxHistorySize;
    bool m_autoSaveEnabled;
    int m_autoSaveInterval;
    QString m_historyFilePath;
    
    // 自动保存
    QTimer *m_autoSaveTimer;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 统计
    QMap<QString, int> m_commandUsageCount;
};

#endif // COMMANDHISTORY_H
