#include <QApplication>
#include <QMainWindow>
#include <QTextEdit>
#include <QVBoxLayout>
#include <QWidget>
#include <QDebug>

int main(int argc, char *argv[])
{
    // 设置高DPI属性
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    QApplication app(argc, argv);
    
    qDebug() << "Creating simple MainWindow...";
    
    // 创建主窗口
    QMainWindow window;
    window.setWindowTitle("RF调试工具 v2.0 - Qt版本");
    window.setMinimumSize(1200, 800);
    
    // 创建中央部件
    QWidget *centralWidget = new QWidget;
    window.setCentralWidget(centralWidget);
    
    // 创建布局
    QVBoxLayout *layout = new QVBoxLayout(centralWidget);
    
    // 创建简单的文本编辑器代替InteractiveTerminal
    QTextEdit *textEdit = new QTextEdit;
    textEdit->setPlainText("=== RF调试工具 Qt版本 ===\n\n"
                          "这是一个简化版本的测试窗口。\n"
                          "如果您能看到这个窗口，说明基本的Qt功能正常。\n\n"
                          "原始程序可能在InteractiveTerminal或其他组件中有问题。");
    
    layout->addWidget(textEdit);
    
    // 显示窗口
    qDebug() << "Showing window...";
    window.show();
    window.raise();
    window.activateWindow();
    
    qDebug() << "Window shown successfully";
    qDebug() << "Window visible:" << window.isVisible();
    qDebug() << "Window size:" << window.size();
    
    return app.exec();
}
