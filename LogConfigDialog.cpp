#include "LogConfigDialog.h"
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>

LogConfigDialog::LogConfigDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("日志配置");
    setModal(true);
    resize(500, 400);
    
    setupUI();
    loadConfig();
}

LogConfigDialog::~LogConfigDialog()
{
}

void LogConfigDialog::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 时间戳配置组
    m_timestampGroup = new QGroupBox("时间戳配置");
    QFormLayout *timestampLayout = new QFormLayout(m_timestampGroup);
    
    m_timestampEnabledCheck = new QCheckBox("启用时间戳");
    timestampLayout->addRow(m_timestampEnabledCheck);
    
    m_timestampFormatCombo = new QComboBox;
    m_timestampFormatCombo->addItem("hh:mm:ss", "hh:mm:ss");
    m_timestampFormatCombo->addItem("yyyy-MM-dd hh:mm:ss", "yyyy-MM-dd hh:mm:ss");
    m_timestampFormatCombo->addItem("MM-dd hh:mm:ss", "MM-dd hh:mm:ss");
    m_timestampFormatCombo->addItem("自定义", "custom");
    timestampLayout->addRow("时间格式:", m_timestampFormatCombo);
    
    m_customFormatEdit = new QLineEdit;
    m_customFormatEdit->setPlaceholderText("例如: yyyy-MM-dd hh:mm:ss.zzz");
    timestampLayout->addRow("自定义格式:", m_customFormatEdit);
    
    // 文件配置组
    m_fileGroup = new QGroupBox("文件配置");
    QFormLayout *fileLayout = new QFormLayout(m_fileGroup);
    
    m_fileEnabledCheck = new QCheckBox("启用日志文件");
    fileLayout->addRow(m_fileEnabledCheck);
    
    QHBoxLayout *filePathLayout = new QHBoxLayout;
    m_filePathEdit = new QLineEdit;
    m_browseFileBtn = new QPushButton("浏览...");
    filePathLayout->addWidget(m_filePathEdit);
    filePathLayout->addWidget(m_browseFileBtn);
    fileLayout->addRow("日志文件:", filePathLayout);
    
    m_autoSaveCheck = new QCheckBox("自动保存");
    fileLayout->addRow(m_autoSaveCheck);
    
    m_autoSaveIntervalSpin = new QSpinBox;
    m_autoSaveIntervalSpin->setRange(1, 3600);
    m_autoSaveIntervalSpin->setSuffix(" 秒");
    fileLayout->addRow("保存间隔:", m_autoSaveIntervalSpin);
    
    // 显示配置组
    m_displayGroup = new QGroupBox("显示配置");
    QFormLayout *displayLayout = new QFormLayout(m_displayGroup);
    
    m_echoEnabledCheck = new QCheckBox("启用回显");
    displayLayout->addRow(m_echoEnabledCheck);
    
    m_logLevelCombo = new QComboBox;
    m_logLevelCombo->addItem("调试", 0);
    m_logLevelCombo->addItem("信息", 1);
    m_logLevelCombo->addItem("警告", 2);
    m_logLevelCombo->addItem("错误", 3);
    displayLayout->addRow("日志级别:", m_logLevelCombo);
    
    m_maxLinesSpin = new QSpinBox;
    m_maxLinesSpin->setRange(100, 100000);
    m_maxLinesSpin->setSuffix(" 行");
    displayLayout->addRow("最大行数:", m_maxLinesSpin);
    
    // 文件轮转配置组
    m_rotationGroup = new QGroupBox("文件轮转");
    QFormLayout *rotationLayout = new QFormLayout(m_rotationGroup);
    
    m_rotationEnabledCheck = new QCheckBox("启用文件轮转");
    rotationLayout->addRow(m_rotationEnabledCheck);
    
    m_maxFileSizeSpin = new QSpinBox;
    m_maxFileSizeSpin->setRange(1, 1000);
    m_maxFileSizeSpin->setSuffix(" MB");
    rotationLayout->addRow("最大文件大小:", m_maxFileSizeSpin);
    
    m_maxBackupFilesSpin = new QSpinBox;
    m_maxBackupFilesSpin->setRange(1, 100);
    rotationLayout->addRow("备份文件数:", m_maxBackupFilesSpin);
    
    // 按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    m_okBtn = new QPushButton("确定");
    m_cancelBtn = new QPushButton("取消");
    m_resetBtn = new QPushButton("重置");
    m_testBtn = new QPushButton("测试");
    
    buttonLayout->addWidget(m_testBtn);
    buttonLayout->addWidget(m_resetBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_okBtn);
    buttonLayout->addWidget(m_cancelBtn);
    
    // 添加到主布局
    mainLayout->addWidget(m_timestampGroup);
    mainLayout->addWidget(m_fileGroup);
    mainLayout->addWidget(m_displayGroup);
    mainLayout->addWidget(m_rotationGroup);
    mainLayout->addLayout(buttonLayout);
    
    // 连接信号
    connect(m_browseFileBtn, &QPushButton::clicked, this, &LogConfigDialog::onBrowseLogFile);
    connect(m_resetBtn, &QPushButton::clicked, this, &LogConfigDialog::onResetToDefaults);
    connect(m_testBtn, &QPushButton::clicked, this, &LogConfigDialog::onTestLogFile);
    connect(m_okBtn, &QPushButton::clicked, this, &LogConfigDialog::accept);
    connect(m_cancelBtn, &QPushButton::clicked, this, &LogConfigDialog::reject);
}

void LogConfigDialog::loadConfig()
{
    // 设置默认值
    m_timestampEnabledCheck->setChecked(true);
    m_timestampFormatCombo->setCurrentIndex(1);
    m_fileEnabledCheck->setChecked(false);
    m_autoSaveCheck->setChecked(true);
    m_autoSaveIntervalSpin->setValue(30);
    m_echoEnabledCheck->setChecked(true);
    m_logLevelCombo->setCurrentIndex(1);
    m_maxLinesSpin->setValue(10000);
    m_rotationEnabledCheck->setChecked(false);
    m_maxFileSizeSpin->setValue(10);
    m_maxBackupFilesSpin->setValue(5);
    
    // 设置默认日志文件路径
    QString defaultPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(defaultPath);
    m_filePathEdit->setText(QDir(defaultPath).filePath("rf_tool.log"));
}

void LogConfigDialog::onBrowseLogFile()
{
    QString fileName = QFileDialog::getSaveFileName(this, "选择日志文件", 
                                                   m_filePathEdit->text(), 
                                                   "日志文件 (*.log);;所有文件 (*.*)");
    if (!fileName.isEmpty()) {
        m_filePathEdit->setText(fileName);
    }
}

void LogConfigDialog::onResetToDefaults()
{
    loadConfig();
}

void LogConfigDialog::onTestLogFile()
{
    QString filePath = m_filePathEdit->text();
    if (filePath.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先设置日志文件路径");
        return;
    }
    
    QMessageBox::information(this, "测试", "日志文件路径有效");
}

void LogConfigDialog::accept()
{
    saveConfig();
    QDialog::accept();
}

void LogConfigDialog::reject()
{
    QDialog::reject();
}

void LogConfigDialog::saveConfig()
{
    // 发送配置更改信号
    emit configChanged("log_timestamp_enabled", m_timestampEnabledCheck->isChecked());
    emit configChanged("log_timestamp_format", m_timestampFormatCombo->currentData().toString());
    emit configChanged("log_file_enabled", m_fileEnabledCheck->isChecked());
    emit configChanged("log_file_path", m_filePathEdit->text());
    emit configChanged("log_auto_save", m_autoSaveCheck->isChecked());
    emit configChanged("log_auto_save_interval", m_autoSaveIntervalSpin->value());
    emit configChanged("log_echo_enabled", m_echoEnabledCheck->isChecked());
    emit configChanged("log_level", m_logLevelCombo->currentData().toInt());
    emit configChanged("log_max_lines", m_maxLinesSpin->value());
    emit configChanged("log_rotation_enabled", m_rotationEnabledCheck->isChecked());
    emit configChanged("log_max_file_size", m_maxFileSizeSpin->value());
    emit configChanged("log_max_backup_files", m_maxBackupFilesSpin->value());
}

void LogConfigDialog::setConfig(const QVariantMap &config)
{
    m_config = config;
    updateUI();
}

QVariantMap LogConfigDialog::getConfig() const
{
    return m_config;
}

void LogConfigDialog::updateUI()
{
    // 根据配置更新UI
    // 这里可以添加从配置加载UI状态的代码
}
