#include "CommandListWidget.h"
#include <QInputDialog>
#include <QMessageBox>
#include <QApplication>
#include <QClipboard>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QFileDialog>
#include <QDateTime>

CommandListWidget::CommandListWidget(QWidget *parent)
    : QListWidget(parent), m_sortOrder(ByName), m_sortDirection(Qt::AscendingOrder), m_showDescription(true), m_showUsageCount(false)
{
    setupContextMenu();

    // 设置过滤定时器
    m_filterTimer = new QTimer(this);
    m_filterTimer->setSingleShot(true);
    m_filterTimer->setInterval(300);
    connect(m_filterTimer, &QTimer::timeout, this, &CommandListWidget::onFilterTimer);

    // 设置列表属性
    setAlternatingRowColors(true);
    setSelectionMode(QAbstractItemView::SingleSelection);
    setDragDropMode(QAbstractItemView::InternalMove);
}

CommandListWidget::~CommandListWidget()
{
}

void CommandListWidget::addCommand(const QString &name, const QString &content, const QString &description)
{
    if (name.trimmed().isEmpty() || content.trimmed().isEmpty())
    {
        return;
    }

    CommandItem item;
    item.name = name.trimmed();
    item.content = content.trimmed();
    item.description = description.trimmed();
    item.enabled = true;
    item.created = QDateTime::currentDateTime();
    item.lastUsed = QDateTime();
    item.usageCount = 0;

    m_commands[item.name] = item;
    updateDisplay();

    emit commandAdded(item.name, item.content);
}

void CommandListWidget::removeCommand(const QString &name)
{
    if (m_commands.contains(name))
    {
        m_commands.remove(name);
        updateDisplay();
        emit commandDeleted(name);
    }
}

void CommandListWidget::updateCommand(const QString &name, const QString &newName, const QString &content, const QString &description)
{
    if (!m_commands.contains(name))
    {
        return;
    }

    CommandItem item = m_commands[name];

    // 如果名称改变，需要删除旧的并添加新的
    if (name != newName)
    {
        m_commands.remove(name);
        item.name = newName;
    }

    item.content = content;
    item.description = description;

    m_commands[item.name] = item;
    updateDisplay();

    emit commandEdited(name, newName, content);
}

void CommandListWidget::clearCommands()
{
    m_commands.clear();
    clear();
}

void CommandListWidget::enableCommand(const QString &name, bool enabled)
{
    if (m_commands.contains(name))
    {
        m_commands[name].enabled = enabled;
        updateDisplay();
        emit commandEnabled(name, enabled);
    }
}

void CommandListWidget::disableCommand(const QString &name)
{
    enableCommand(name, false);
}

bool CommandListWidget::isCommandEnabled(const QString &name) const
{
    if (m_commands.contains(name))
    {
        return m_commands[name].enabled;
    }
    return false;
}

QStringList CommandListWidget::getCommandNames() const
{
    return m_commands.keys();
}

CommandListWidget::CommandItem CommandListWidget::getCommand(const QString &name) const
{
    return m_commands.value(name);
}

QList<CommandListWidget::CommandItem> CommandListWidget::getAllCommands() const
{
    return m_commands.values();
}

int CommandListWidget::commandCount() const
{
    return m_commands.size();
}

void CommandListWidget::setFilter(const QString &filter)
{
    m_filter = filter;
    m_filterTimer->start();
}

QString CommandListWidget::currentFilter() const
{
    return m_filter;
}

void CommandListWidget::clearFilter()
{
    m_filter.clear();
    applyFilter();
}

void CommandListWidget::setSortOrder(SortOrder order, Qt::SortOrder direction)
{
    m_sortOrder = order;
    m_sortDirection = direction;
    sortCommands();
}

CommandListWidget::SortOrder CommandListWidget::currentSortOrder() const
{
    return m_sortOrder;
}

void CommandListWidget::setupContextMenu()
{
    m_contextMenu = new QMenu(this);

    m_executeAction = m_contextMenu->addAction("执行命令", this, &CommandListWidget::onExecuteCommand);
    m_contextMenu->addSeparator();

    m_editAction = m_contextMenu->addAction("编辑命令", this, &CommandListWidget::onEditCommand);
    m_deleteAction = m_contextMenu->addAction("删除命令", this, &CommandListWidget::onDeleteCommand);
    m_contextMenu->addSeparator();

    m_toggleAction = m_contextMenu->addAction("启用/禁用", this, &CommandListWidget::onToggleCommand);
    m_copyAction = m_contextMenu->addAction("复制命令", this, &CommandListWidget::onCopyCommand);
    m_contextMenu->addSeparator();

    m_moveUpAction = m_contextMenu->addAction("上移", this, &CommandListWidget::onMoveUp);
    m_moveDownAction = m_contextMenu->addAction("下移", this, &CommandListWidget::onMoveDown);
    m_contextMenu->addSeparator();

    m_propertiesAction = m_contextMenu->addAction("属性", this, &CommandListWidget::onShowProperties);
}

void CommandListWidget::updateDisplay()
{
    clear();

    for (auto it = m_commands.begin(); it != m_commands.end(); ++it)
    {
        const CommandItem &command = it.value();

        QListWidgetItem *item = new QListWidgetItem;
        updateItem(item, command);
        addItem(item);
    }

    sortCommands();
    applyFilter();
}

void CommandListWidget::updateItem(QListWidgetItem *item, const CommandItem &command)
{
    item->setText(formatItemText(command));
    item->setData(Qt::UserRole, command.name);

    // 设置图标和样式
    if (command.enabled)
    {
        item->setForeground(QBrush(Qt::black));
        item->setIcon(QIcon(":/icons/command_enabled.png"));
    }
    else
    {
        item->setForeground(QBrush(Qt::gray));
        item->setIcon(QIcon(":/icons/command_disabled.png"));
    }

    // 设置工具提示
    QString tooltip = QString("命令: %1\n内容: %2").arg(command.name, command.content);
    if (!command.description.isEmpty())
    {
        tooltip += QString("\n描述: %1").arg(command.description);
    }
    tooltip += QString("\n创建时间: %1").arg(command.created.toString());
    if (!command.lastUsed.isNull())
    {
        tooltip += QString("\n最后使用: %1").arg(command.lastUsed.toString());
    }
    tooltip += QString("\n使用次数: %1").arg(command.usageCount);

    item->setToolTip(tooltip);
}

QString CommandListWidget::formatItemText(const CommandItem &command) const
{
    QString text = command.name;

    if (m_showDescription && !command.description.isEmpty())
    {
        text += QString(" - %1").arg(command.description);
    }

    if (m_showUsageCount)
    {
        text += QString(" (%1)").arg(command.usageCount);
    }

    return text;
}

void CommandListWidget::contextMenuEvent(QContextMenuEvent *event)
{
    QListWidgetItem *item = itemAt(event->pos());
    if (!item)
    {
        return;
    }

    m_currentCommand = item->data(Qt::UserRole).toString();

    // 更新菜单项状态
    bool hasCommand = !m_currentCommand.isEmpty();
    bool isEnabled = hasCommand && isCommandEnabled(m_currentCommand);

    m_executeAction->setEnabled(hasCommand && isEnabled);
    m_editAction->setEnabled(hasCommand);
    m_deleteAction->setEnabled(hasCommand);
    m_toggleAction->setEnabled(hasCommand);
    m_toggleAction->setText(isEnabled ? "禁用" : "启用");
    m_copyAction->setEnabled(hasCommand);

    int currentRow = row(item);
    m_moveUpAction->setEnabled(hasCommand && currentRow > 0);
    m_moveDownAction->setEnabled(hasCommand && currentRow < count() - 1);

    m_propertiesAction->setEnabled(hasCommand);

    m_contextMenu->exec(event->globalPos());
}

void CommandListWidget::mouseDoubleClickEvent(QMouseEvent *event)
{
    QListWidgetItem *item = itemAt(event->pos());
    if (item)
    {
        QString commandName = item->data(Qt::UserRole).toString();
        if (isCommandEnabled(commandName))
        {
            CommandItem command = getCommand(commandName);

            // 更新使用统计
            m_commands[commandName].lastUsed = QDateTime::currentDateTime();
            m_commands[commandName].usageCount++;

            emit executeCommand(command.name, command.content);
        }
    }

    QListWidget::mouseDoubleClickEvent(event);
}

void CommandListWidget::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter)
    {
        QListWidgetItem *item = currentItem();
        if (item)
        {
            QString commandName = item->data(Qt::UserRole).toString();
            if (isCommandEnabled(commandName))
            {
                CommandItem command = getCommand(commandName);

                // 更新使用统计
                m_commands[commandName].lastUsed = QDateTime::currentDateTime();
                m_commands[commandName].usageCount++;

                emit executeCommand(command.name, command.content);
            }
        }
    }
    else if (event->key() == Qt::Key_Delete)
    {
        onDeleteCommand();
    }
    else
    {
        QListWidget::keyPressEvent(event);
    }
}

void CommandListWidget::onExecuteCommand()
{
    if (m_currentCommand.isEmpty())
    {
        return;
    }

    CommandItem command = getCommand(m_currentCommand);
    if (command.enabled)
    {
        // 更新使用统计
        m_commands[m_currentCommand].lastUsed = QDateTime::currentDateTime();
        m_commands[m_currentCommand].usageCount++;

        emit executeCommand(command.name, command.content);
    }
}

void CommandListWidget::onEditCommand()
{
    if (m_currentCommand.isEmpty())
    {
        return;
    }

    CommandItem command = getCommand(m_currentCommand);

    bool ok;
    QString newName = QInputDialog::getText(this, "编辑命令", "命令名称:", QLineEdit::Normal, command.name, &ok);
    if (!ok || newName.trimmed().isEmpty())
    {
        return;
    }

    QString newContent = QInputDialog::getMultiLineText(this, "编辑命令", "命令内容:", command.content, &ok);
    if (!ok)
    {
        return;
    }

    QString newDescription = QInputDialog::getText(this, "编辑命令", "命令描述:", QLineEdit::Normal, command.description, &ok);
    if (!ok)
    {
        newDescription = command.description;
    }

    updateCommand(m_currentCommand, newName.trimmed(), newContent.trimmed(), newDescription.trimmed());
}

void CommandListWidget::onDeleteCommand()
{
    if (m_currentCommand.isEmpty())
    {
        return;
    }

    int ret = QMessageBox::question(this, "删除命令",
                                    QString("确定要删除命令 '%1' 吗？").arg(m_currentCommand),
                                    QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes)
    {
        removeCommand(m_currentCommand);
    }
}

void CommandListWidget::onToggleCommand()
{
    if (m_currentCommand.isEmpty())
    {
        return;
    }

    bool currentState = isCommandEnabled(m_currentCommand);
    enableCommand(m_currentCommand, !currentState);
}

void CommandListWidget::onCopyCommand()
{
    if (m_currentCommand.isEmpty())
    {
        return;
    }

    CommandItem command = getCommand(m_currentCommand);
    QApplication::clipboard()->setText(command.content);
}

void CommandListWidget::onMoveUp()
{
    QListWidgetItem *item = currentItem();
    if (!item)
    {
        return;
    }

    int currentRow = row(item);
    if (currentRow > 0)
    {
        takeItem(currentRow);
        insertItem(currentRow - 1, item);
        setCurrentItem(item);
    }
}

void CommandListWidget::onMoveDown()
{
    QListWidgetItem *item = currentItem();
    if (!item)
    {
        return;
    }

    int currentRow = row(item);
    if (currentRow < count() - 1)
    {
        takeItem(currentRow);
        insertItem(currentRow + 1, item);
        setCurrentItem(item);
    }
}

void CommandListWidget::onShowProperties()
{
    if (m_currentCommand.isEmpty())
    {
        return;
    }

    CommandItem command = getCommand(m_currentCommand);

    QString info = QString("命令属性\n\n"
                           "名称: %1\n"
                           "内容: %2\n"
                           "描述: %3\n"
                           "状态: %4\n"
                           "创建时间: %5\n"
                           "最后使用: %6\n"
                           "使用次数: %7")
                       .arg(command.name)
                       .arg(command.content)
                       .arg(command.description.isEmpty() ? "无" : command.description)
                       .arg(command.enabled ? "启用" : "禁用")
                       .arg(command.created.toString())
                       .arg(command.lastUsed.isNull() ? "从未使用" : command.lastUsed.toString())
                       .arg(command.usageCount);

    QMessageBox::information(this, "命令属性", info);
}

void CommandListWidget::onFilterTimer()
{
    applyFilter();
}

void CommandListWidget::sortCommands()
{
    // 实现排序逻辑
    // 这里简化处理，实际应该根据m_sortOrder和m_sortDirection进行排序
}

void CommandListWidget::applyFilter()
{
    if (m_filter.isEmpty())
    {
        for (int i = 0; i < count(); ++i)
        {
            item(i)->setHidden(false);
        }
        return;
    }

    for (int i = 0; i < count(); ++i)
    {
        QListWidgetItem *listItem = item(i);
        QString commandName = listItem->data(Qt::UserRole).toString();
        CommandItem command = getCommand(commandName);

        bool matches = command.name.contains(m_filter, Qt::CaseInsensitive) ||
                       command.content.contains(m_filter, Qt::CaseInsensitive) ||
                       command.description.contains(m_filter, Qt::CaseInsensitive);

        listItem->setHidden(!matches);
    }
}

void CommandListWidget::setShowDescription(bool show)
{
    m_showDescription = show;
    updateDisplay();
}

bool CommandListWidget::isShowDescription() const
{
    return m_showDescription;
}

void CommandListWidget::setShowUsageCount(bool show)
{
    m_showUsageCount = show;
    updateDisplay();
}

bool CommandListWidget::isShowUsageCount() const
{
    return m_showUsageCount;
}

// 导入导出功能实现
bool CommandListWidget::exportCommands(const QString &filePath) const
{
    QJsonArray commandsArray;

    for (auto it = m_commands.begin(); it != m_commands.end(); ++it)
    {
        const CommandItem &command = it.value();
        QJsonObject commandObj;
        commandObj["name"] = command.name;
        commandObj["content"] = command.content;
        commandObj["description"] = command.description;
        commandObj["enabled"] = command.enabled;
        commandObj["created"] = command.created.toString(Qt::ISODate);
        commandObj["lastUsed"] = command.lastUsed.toString(Qt::ISODate);
        commandObj["usageCount"] = command.usageCount;

        commandsArray.append(commandObj);
    }

    QJsonDocument doc(commandsArray);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly))
    {
        return false;
    }

    file.write(doc.toJson());
    return true;
}

bool CommandListWidget::importCommands(const QString &filePath, bool merge)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        return false;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);

    if (!doc.isArray())
    {
        return false;
    }

    if (!merge)
    {
        clearCommands();
    }

    QJsonArray commandsArray = doc.array();
    for (const QJsonValue &value : commandsArray)
    {
        if (!value.isObject())
        {
            continue;
        }

        QJsonObject commandObj = value.toObject();
        CommandItem command;
        command.name = commandObj["name"].toString();
        command.content = commandObj["content"].toString();
        command.description = commandObj["description"].toString();
        command.enabled = commandObj["enabled"].toBool(true);
        command.created = QDateTime::fromString(commandObj["created"].toString(), Qt::ISODate);
        command.lastUsed = QDateTime::fromString(commandObj["lastUsed"].toString(), Qt::ISODate);
        command.usageCount = commandObj["usageCount"].toInt(0);

        if (!command.name.isEmpty() && !command.content.isEmpty())
        {
            m_commands[command.name] = command;
        }
    }

    updateDisplay();
    return true;
}

QVariantList CommandListWidget::toVariantList() const
{
    QVariantList list;

    for (auto it = m_commands.begin(); it != m_commands.end(); ++it)
    {
        const CommandItem &command = it.value();
        QVariantMap commandMap;
        commandMap["name"] = command.name;
        commandMap["content"] = command.content;
        commandMap["description"] = command.description;
        commandMap["enabled"] = command.enabled;
        commandMap["created"] = command.created;
        commandMap["lastUsed"] = command.lastUsed;
        commandMap["usageCount"] = command.usageCount;

        list.append(commandMap);
    }

    return list;
}

void CommandListWidget::fromVariantList(const QVariantList &commands, bool merge)
{
    if (!merge)
    {
        clearCommands();
    }

    for (const QVariant &variant : commands)
    {
        QVariantMap commandMap = variant.toMap();
        CommandItem command;
        command.name = commandMap["name"].toString();
        command.content = commandMap["content"].toString();
        command.description = commandMap["description"].toString();
        command.enabled = commandMap["enabled"].toBool(true);
        command.created = commandMap["created"].toDateTime();
        command.lastUsed = commandMap["lastUsed"].toDateTime();
        command.usageCount = commandMap["usageCount"].toInt(0);

        if (!command.name.isEmpty() && !command.content.isEmpty())
        {
            m_commands[command.name] = command;
        }
    }

    updateDisplay();
}
