/****************************************************************************
** Meta object code from reading C++ file 'SSHConnection.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../SSHConnection.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'SSHConnection.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SSHConnection_t {
    QByteArrayData data[25];
    char stringdata0[349];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SSHConnection_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SSHConnection_t qt_meta_stringdata_SSHConnection = {
    {
QT_MOC_LITERAL(0, 0, 13), // "SSHConnection"
QT_MOC_LITERAL(1, 14, 9), // "connected"
QT_MOC_LITERAL(2, 24, 0), // ""
QT_MOC_LITERAL(3, 25, 12), // "disconnected"
QT_MOC_LITERAL(4, 38, 12), // "dataReceived"
QT_MOC_LITERAL(5, 51, 4), // "data"
QT_MOC_LITERAL(6, 56, 13), // "errorOccurred"
QT_MOC_LITERAL(7, 70, 5), // "error"
QT_MOC_LITERAL(8, 76, 12), // "stateChanged"
QT_MOC_LITERAL(9, 89, 15), // "ConnectionState"
QT_MOC_LITERAL(10, 105, 5), // "state"
QT_MOC_LITERAL(11, 111, 22), // "authenticationRequired"
QT_MOC_LITERAL(12, 134, 10), // "AuthMethod"
QT_MOC_LITERAL(13, 145, 6), // "method"
QT_MOC_LITERAL(14, 152, 27), // "hostKeyVerificationRequired"
QT_MOC_LITERAL(15, 180, 7), // "hostKey"
QT_MOC_LITERAL(16, 188, 9), // "reconnect"
QT_MOC_LITERAL(17, 198, 13), // "sendKeepAlive"
QT_MOC_LITERAL(18, 212, 17), // "onSocketConnected"
QT_MOC_LITERAL(19, 230, 20), // "onSocketDisconnected"
QT_MOC_LITERAL(20, 251, 13), // "onSocketError"
QT_MOC_LITERAL(21, 265, 28), // "QAbstractSocket::SocketError"
QT_MOC_LITERAL(22, 294, 17), // "onSocketReadyRead"
QT_MOC_LITERAL(23, 312, 19), // "onConnectionTimeout"
QT_MOC_LITERAL(24, 332, 16) // "onKeepAliveTimer"

    },
    "SSHConnection\0connected\0\0disconnected\0"
    "dataReceived\0data\0errorOccurred\0error\0"
    "stateChanged\0ConnectionState\0state\0"
    "authenticationRequired\0AuthMethod\0"
    "method\0hostKeyVerificationRequired\0"
    "hostKey\0reconnect\0sendKeepAlive\0"
    "onSocketConnected\0onSocketDisconnected\0"
    "onSocketError\0QAbstractSocket::SocketError\0"
    "onSocketReadyRead\0onConnectionTimeout\0"
    "onKeepAliveTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SSHConnection[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   89,    2, 0x06 /* Public */,
       3,    0,   90,    2, 0x06 /* Public */,
       4,    1,   91,    2, 0x06 /* Public */,
       6,    1,   94,    2, 0x06 /* Public */,
       8,    1,   97,    2, 0x06 /* Public */,
      11,    1,  100,    2, 0x06 /* Public */,
      14,    1,  103,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      16,    0,  106,    2, 0x0a /* Public */,
      17,    0,  107,    2, 0x0a /* Public */,
      18,    0,  108,    2, 0x08 /* Private */,
      19,    0,  109,    2, 0x08 /* Private */,
      20,    1,  110,    2, 0x08 /* Private */,
      22,    0,  113,    2, 0x08 /* Private */,
      23,    0,  114,    2, 0x08 /* Private */,
      24,    0,  115,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QByteArray,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void, 0x80000000 | 12,   13,
    QMetaType::Void, QMetaType::QString,   15,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 21,    7,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void SSHConnection::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SSHConnection *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->connected(); break;
        case 1: _t->disconnected(); break;
        case 2: _t->dataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 3: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->stateChanged((*reinterpret_cast< ConnectionState(*)>(_a[1]))); break;
        case 5: _t->authenticationRequired((*reinterpret_cast< AuthMethod(*)>(_a[1]))); break;
        case 6: _t->hostKeyVerificationRequired((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->reconnect(); break;
        case 8: _t->sendKeepAlive(); break;
        case 9: _t->onSocketConnected(); break;
        case 10: _t->onSocketDisconnected(); break;
        case 11: _t->onSocketError((*reinterpret_cast< QAbstractSocket::SocketError(*)>(_a[1]))); break;
        case 12: _t->onSocketReadyRead(); break;
        case 13: _t->onConnectionTimeout(); break;
        case 14: _t->onKeepAliveTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 11:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SSHConnection::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SSHConnection::connected)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SSHConnection::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SSHConnection::disconnected)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SSHConnection::*)(const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SSHConnection::dataReceived)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SSHConnection::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SSHConnection::errorOccurred)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (SSHConnection::*)(ConnectionState );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SSHConnection::stateChanged)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (SSHConnection::*)(AuthMethod );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SSHConnection::authenticationRequired)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (SSHConnection::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SSHConnection::hostKeyVerificationRequired)) {
                *result = 6;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SSHConnection::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_SSHConnection.data,
    qt_meta_data_SSHConnection,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SSHConnection::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SSHConnection::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SSHConnection.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SSHConnection::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void SSHConnection::connected()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void SSHConnection::disconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void SSHConnection::dataReceived(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void SSHConnection::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void SSHConnection::stateChanged(ConnectionState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void SSHConnection::authenticationRequired(AuthMethod _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void SSHConnection::hostKeyVerificationRequired(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
