#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QTranslator>
#include <QLibraryInfo>
#include <QFont>
#include <QFontDatabase>
#include <QIcon>

#include "MainWindow.h"

void setupApplication(QApplication &app)
{
    // 设置应用程序信息
    app.setApplicationName("RF调试工具");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("RF Tools");
    app.setOrganizationDomain("rftools.com");

    // 设置应用程序图标
    app.setWindowIcon(QIcon(":/icons/app.png"));

    // 设置样式
    app.setStyle(QStyleFactory::create("Fusion"));

    // 设置字体
    QFont font = app.font();
    font.setFamily("Microsoft YaHei UI");
    font.setPointSize(9);
    app.setFont(font);
}

void setupTranslation(QApplication &app)
{
    // 设置中文翻译
    QTranslator *translator = new QTranslator(&app);

    // 尝试加载Qt自带的中文翻译
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QString qtTranslationPath = QLibraryInfo::location(QLibraryInfo::TranslationsPath);
#else
    QString qtTranslationPath = QLibraryInfo::path(QLibraryInfo::TranslationsPath);
#endif
    if (translator->load("qt_zh_CN", qtTranslationPath))
    {
        app.installTranslator(translator);
    }

    // 加载应用程序翻译文件
    QTranslator *appTranslator = new QTranslator(&app);
    if (appTranslator->load(":/translations/rftool_zh_CN.qm"))
    {
        app.installTranslator(appTranslator);
    }
}

void setupMacStyle(QApplication &app)
{
    // Mac风格样式表
    QString styleSheet = R"(
        /* 主窗口样式 */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
            color: #2c3e50;
        }
        
        /* 工具栏样式 */
        QToolBar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            spacing: 3px;
            padding: 5px;
        }
        
        QToolBar::handle {
            background: #6c757d;
            width: 8px;
            margin: 2px;
            border-radius: 4px;
        }
        
        /* 按钮样式 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #6c757d, stop:1 #495057);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            padding: 8px 16px;
            margin: 2px;
        }
        
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #495057, stop:1 #343a40);
        }
        
        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #343a40, stop:1 #495057);
        }
        
        QPushButton:disabled {
            background: #adb5bd;
            color: #6c757d;
        }
        
        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background: white;
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 13px;
            color: #2c3e50;
            selection-background-color: #007bff;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #007bff;
            background: white;
        }
        
        /* 组合框样式 */
        QComboBox {
            background: white;
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 13px;
            color: #2c3e50;
            min-width: 100px;
        }
        
        QComboBox:focus {
            border: 2px solid #007bff;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: url(:/icons/arrow_down.png);
            width: 12px;
            height: 12px;
        }
        
        /* 列表控件样式 */
        QListWidget {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 5px;
            font-size: 13px;
            color: #495057;
        }
        
        QListWidget::item {
            background: transparent;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 2px;
        }
        
        QListWidget::item:selected {
            background: #007bff;
            color: white;
        }
        
        QListWidget::item:hover {
            background: #e9ecef;
        }
        
        /* 标签页样式 */
        QTabWidget::pane {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
            margin-top: 5px;
        }
        
        QTabBar::tab {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-size: 13px;
            color: #495057;
        }
        
        QTabBar::tab:selected {
            background: white;
            border-bottom: 1px solid white;
            color: #007bff;
            font-weight: bold;
        }
        
        QTabBar::tab:hover {
            background: #e9ecef;
        }
        
        /* 分组框样式 */
        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            color: #495057;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #007bff;
        }
        
        /* 状态栏样式 */
        QStatusBar {
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        
        /* 菜单样式 */
        QMenuBar {
            background: transparent;
            color: #495057;
            font-size: 13px;
        }
        
        QMenuBar::item {
            background: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }
        
        QMenuBar::item:selected {
            background: #e9ecef;
        }
        
        QMenu {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 5px;
        }
        
        QMenu::item {
            padding: 8px 20px;
            border-radius: 4px;
        }
        
        QMenu::item:selected {
            background: #007bff;
            color: white;
        }
        
        /* 滚动条样式 */
        QScrollBar:vertical {
            background: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background: #ced4da;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background: #adb5bd;
        }
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
    )";

    app.setStyleSheet(styleSheet);
}

int main(int argc, char *argv[])
{
    // 必须在创建QApplication之前设置高DPI属性
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);

    QApplication app(argc, argv);

    // 设置应用程序
    setupApplication(app);

    // 设置翻译
    setupTranslation(app);

    // 应用Mac风格
    setupMacStyle(app);

    // 创建主窗口
    MainWindow window;
    window.show();

    return app.exec();
}
