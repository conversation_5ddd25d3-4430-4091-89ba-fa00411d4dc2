QT += core widgets serialport network

CONFIG += c++17

TARGET = RFTool_Qt
TEMPLATE = app

# 源文件
SOURCES += \
    main.cpp \
    MainWindow.cpp \
    ConnectionManager.cpp \
    ConfigManager.cpp \
    LogManager.cpp \
    InteractiveTerminal.cpp \
    CommandHistory.cpp \
    CommandListWidget.cpp \
    SerialConnection.cpp \
    TCPConnection.cpp \
    ADBConnection.cpp \
    ConnectionDialog.cpp \
    LogConfigDialog.cpp \
    BackgroundConfigDialog.cpp

# 头文件
HEADERS += \
    MainWindow.h \
    ConnectionManager.h \
    ConfigManager.h \
    LogManager.h \
    InteractiveTerminal.h \
    CommandHistory.h \
    CommandListWidget.h \
    SerialConnection.h \
    TCPConnection.h \
    ADBConnection.h \
    ConnectionDialog.h \
    LogConfigDialog.h \
    BackgroundConfigDialog.h

# 编译器设置
win32 {
    CONFIG += console
    DEFINES += WIN32_LEAN_AND_MEAN
}

# 输出目录
DESTDIR = $$PWD/bin
OBJECTS_DIR = $$PWD/build/obj
MOC_DIR = $$PWD/build/moc
RCC_DIR = $$PWD/build/rcc
UI_DIR = $$PWD/build/ui

# 版本信息
VERSION = 2.0.0
QMAKE_TARGET_COMPANY = "RF Tool Team"
QMAKE_TARGET_PRODUCT = "RF Tool Qt"
QMAKE_TARGET_DESCRIPTION = "RF Debugging Tool Qt Version"
QMAKE_TARGET_COPYRIGHT = "Copyright 2025"
