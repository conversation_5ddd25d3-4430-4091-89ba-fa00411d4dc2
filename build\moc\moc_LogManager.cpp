/****************************************************************************
** Meta object code from reading C++ file 'LogManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../LogManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'LogManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_LogManager_t {
    QByteArrayData data[24];
    char stringdata0[202];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_LogManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_LogManager_t qt_meta_stringdata_LogManager = {
    {
QT_MOC_LITERAL(0, 0, 10), // "LogManager"
QT_MOC_LITERAL(1, 11, 8), // "logSaved"
QT_MOC_LITERAL(2, 20, 0), // ""
QT_MOC_LITERAL(3, 21, 7), // "message"
QT_MOC_LITERAL(4, 29, 13), // "errorOccurred"
QT_MOC_LITERAL(5, 43, 5), // "error"
QT_MOC_LITERAL(6, 49, 13), // "configChanged"
QT_MOC_LITERAL(7, 63, 3), // "key"
QT_MOC_LITERAL(8, 67, 5), // "value"
QT_MOC_LITERAL(9, 73, 11), // "fileRotated"
QT_MOC_LITERAL(10, 85, 7), // "oldFile"
QT_MOC_LITERAL(11, 93, 7), // "newFile"
QT_MOC_LITERAL(12, 101, 5), // "flush"
QT_MOC_LITERAL(13, 107, 10), // "rotateLogs"
QT_MOC_LITERAL(14, 118, 15), // "onAutoSaveTimer"
QT_MOC_LITERAL(15, 134, 13), // "checkFileSize"
QT_MOC_LITERAL(16, 148, 8), // "LogLevel"
QT_MOC_LITERAL(17, 157, 5), // "Debug"
QT_MOC_LITERAL(18, 163, 4), // "Info"
QT_MOC_LITERAL(19, 168, 7), // "Warning"
QT_MOC_LITERAL(20, 176, 5), // "Error"
QT_MOC_LITERAL(21, 182, 6), // "System"
QT_MOC_LITERAL(22, 189, 5), // "Input"
QT_MOC_LITERAL(23, 195, 6) // "Output"

    },
    "LogManager\0logSaved\0\0message\0errorOccurred\0"
    "error\0configChanged\0key\0value\0fileRotated\0"
    "oldFile\0newFile\0flush\0rotateLogs\0"
    "onAutoSaveTimer\0checkFileSize\0LogLevel\0"
    "Debug\0Info\0Warning\0Error\0System\0Input\0"
    "Output"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_LogManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       1,   74, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   54,    2, 0x06 /* Public */,
       4,    1,   57,    2, 0x06 /* Public */,
       6,    2,   60,    2, 0x06 /* Public */,
       9,    2,   65,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      12,    0,   70,    2, 0x0a /* Public */,
      13,    0,   71,    2, 0x0a /* Public */,
      14,    0,   72,    2, 0x08 /* Private */,
      15,    0,   73,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString, QMetaType::QVariant,    7,    8,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   10,   11,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // enums: name, alias, flags, count, data
      16,   16, 0x0,    7,   79,

 // enum data: key, value
      17, uint(LogManager::Debug),
      18, uint(LogManager::Info),
      19, uint(LogManager::Warning),
      20, uint(LogManager::Error),
      21, uint(LogManager::System),
      22, uint(LogManager::Input),
      23, uint(LogManager::Output),

       0        // eod
};

void LogManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<LogManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->logSaved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->configChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QVariant(*)>(_a[2]))); break;
        case 3: _t->fileRotated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->flush(); break;
        case 5: _t->rotateLogs(); break;
        case 6: _t->onAutoSaveTimer(); break;
        case 7: _t->checkFileSize(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (LogManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LogManager::logSaved)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (LogManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LogManager::errorOccurred)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (LogManager::*)(const QString & , const QVariant & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LogManager::configChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (LogManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LogManager::fileRotated)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject LogManager::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_LogManager.data,
    qt_meta_data_LogManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *LogManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *LogManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_LogManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int LogManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void LogManager::logSaved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void LogManager::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void LogManager::configChanged(const QString & _t1, const QVariant & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void LogManager::fileRotated(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
