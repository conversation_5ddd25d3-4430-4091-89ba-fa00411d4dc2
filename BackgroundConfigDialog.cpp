#include "BackgroundConfigDialog.h"
#include <QMessageBox>
#include <QPixmap>

BackgroundConfigDialog::BackgroundConfigDialog(QWidget *parent)
    : QDialog(parent)
    , m_currentColor("#f8f9fa")
    , m_currentColor2("#e9ecef")
    , m_currentOpacity(1.0)
{
    setWindowTitle("背景配置");
    setModal(true);
    resize(500, 600);
    
    setupUI();
    loadConfig();
}

BackgroundConfigDialog::~BackgroundConfigDialog()
{
}

void BackgroundConfigDialog::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 背景类型组
    m_typeGroup = new QGroupBox("背景类型");
    QVBoxLayout *typeLayout = new QVBoxLayout(m_typeGroup);
    
    m_colorRadio = new QRadioButton("纯色背景");
    m_gradientRadio = new QRadioButton("渐变背景");
    m_imageRadio = new QRadioButton("图片背景");
    
    typeLayout->addWidget(m_colorRadio);
    typeLayout->addWidget(m_gradientRadio);
    typeLayout->addWidget(m_imageRadio);
    
    m_colorRadio->setChecked(true);
    
    // 颜色配置组
    m_colorGroup = new QGroupBox("颜色配置");
    QFormLayout *colorLayout = new QFormLayout(m_colorGroup);
    
    QHBoxLayout *color1Layout = new QHBoxLayout;
    m_colorBtn = new QPushButton;
    m_colorBtn->setFixedSize(50, 30);
    m_colorBtn->setStyleSheet(QString("background-color: %1; border: 1px solid #ccc;").arg(m_currentColor));
    m_colorLabel = new QLabel(m_currentColor);
    color1Layout->addWidget(m_colorBtn);
    color1Layout->addWidget(m_colorLabel);
    color1Layout->addStretch();
    colorLayout->addRow("主颜色:", color1Layout);
    
    QHBoxLayout *color2Layout = new QHBoxLayout;
    m_color2Btn = new QPushButton;
    m_color2Btn->setFixedSize(50, 30);
    m_color2Btn->setStyleSheet(QString("background-color: %1; border: 1px solid #ccc;").arg(m_currentColor2));
    m_color2Label = new QLabel(m_currentColor2);
    color2Layout->addWidget(m_color2Btn);
    color2Layout->addWidget(m_color2Label);
    color2Layout->addStretch();
    colorLayout->addRow("渐变颜色:", color2Layout);
    
    // 图片配置组
    m_imageGroup = new QGroupBox("图片配置");
    QFormLayout *imageLayout = new QFormLayout(m_imageGroup);
    
    QHBoxLayout *imagePathLayout = new QHBoxLayout;
    m_imagePathEdit = new QLineEdit;
    m_browseImageBtn = new QPushButton("浏览...");
    imagePathLayout->addWidget(m_imagePathEdit);
    imagePathLayout->addWidget(m_browseImageBtn);
    imageLayout->addRow("图片路径:", imagePathLayout);
    
    m_imageModeCombo = new QComboBox;
    m_imageModeCombo->addItem("拉伸", "stretch");
    m_imageModeCombo->addItem("居中", "center");
    m_imageModeCombo->addItem("平铺", "tile");
    m_imageModeCombo->addItem("适应", "fit");
    imageLayout->addRow("显示模式:", m_imageModeCombo);
    
    // 透明度配置组
    m_opacityGroup = new QGroupBox("透明度配置");
    QFormLayout *opacityLayout = new QFormLayout(m_opacityGroup);
    
    QHBoxLayout *opacitySliderLayout = new QHBoxLayout;
    m_opacitySlider = new QSlider(Qt::Horizontal);
    m_opacitySlider->setRange(1, 100);
    m_opacitySlider->setValue(100);
    m_opacityValueLabel = new QLabel("100%");
    opacitySliderLayout->addWidget(m_opacitySlider);
    opacitySliderLayout->addWidget(m_opacityValueLabel);
    opacityLayout->addRow("透明度:", opacitySliderLayout);
    
    // 预览组
    m_previewGroup = new QGroupBox("预览");
    QVBoxLayout *previewLayout = new QVBoxLayout(m_previewGroup);
    
    m_previewLabel = new QLabel;
    m_previewLabel->setFixedSize(200, 150);
    m_previewLabel->setStyleSheet("border: 1px solid #ccc;");
    m_previewLabel->setAlignment(Qt::AlignCenter);
    m_previewLabel->setText("预览区域");
    previewLayout->addWidget(m_previewLabel, 0, Qt::AlignCenter);
    
    // 按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    m_previewBtn = new QPushButton("预览");
    m_resetBtn = new QPushButton("重置");
    m_okBtn = new QPushButton("确定");
    m_cancelBtn = new QPushButton("取消");
    
    buttonLayout->addWidget(m_previewBtn);
    buttonLayout->addWidget(m_resetBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_okBtn);
    buttonLayout->addWidget(m_cancelBtn);
    
    // 添加到主布局
    mainLayout->addWidget(m_typeGroup);
    mainLayout->addWidget(m_colorGroup);
    mainLayout->addWidget(m_imageGroup);
    mainLayout->addWidget(m_opacityGroup);
    mainLayout->addWidget(m_previewGroup);
    mainLayout->addLayout(buttonLayout);
    
    // 连接信号
    connect(m_colorRadio, &QRadioButton::toggled, this, &BackgroundConfigDialog::onBackgroundTypeChanged);
    connect(m_gradientRadio, &QRadioButton::toggled, this, &BackgroundConfigDialog::onBackgroundTypeChanged);
    connect(m_imageRadio, &QRadioButton::toggled, this, &BackgroundConfigDialog::onBackgroundTypeChanged);
    
    connect(m_colorBtn, &QPushButton::clicked, this, &BackgroundConfigDialog::onChooseColor);
    connect(m_color2Btn, &QPushButton::clicked, this, &BackgroundConfigDialog::onChooseColor2);
    connect(m_browseImageBtn, &QPushButton::clicked, this, &BackgroundConfigDialog::onChooseImage);
    
    connect(m_opacitySlider, &QSlider::valueChanged, this, &BackgroundConfigDialog::onOpacityChanged);
    
    connect(m_previewBtn, &QPushButton::clicked, this, &BackgroundConfigDialog::onPreview);
    connect(m_resetBtn, &QPushButton::clicked, this, &BackgroundConfigDialog::onResetToDefaults);
    connect(m_okBtn, &QPushButton::clicked, this, &BackgroundConfigDialog::accept);
    connect(m_cancelBtn, &QPushButton::clicked, this, &BackgroundConfigDialog::reject);
    
    // 初始化UI状态
    onBackgroundTypeChanged();
}

void BackgroundConfigDialog::loadConfig()
{
    // 设置默认值
    m_colorRadio->setChecked(true);
    m_currentColor = "#f8f9fa";
    m_currentColor2 = "#e9ecef";
    m_currentOpacity = 1.0;
    
    updateUI();
}

void BackgroundConfigDialog::onBackgroundTypeChanged()
{
    bool isColor = m_colorRadio->isChecked();
    bool isGradient = m_gradientRadio->isChecked();
    bool isImage = m_imageRadio->isChecked();
    
    m_colorGroup->setEnabled(isColor || isGradient);
    m_color2Btn->setEnabled(isGradient);
    m_color2Label->setEnabled(isGradient);
    
    m_imageGroup->setEnabled(isImage);
    
    updatePreview();
}

void BackgroundConfigDialog::onChooseColor()
{
    QColor color = QColorDialog::getColor(QColor(m_currentColor), this, "选择主颜色");
    if (color.isValid()) {
        m_currentColor = color.name();
        m_colorBtn->setStyleSheet(QString("background-color: %1; border: 1px solid #ccc;").arg(m_currentColor));
        m_colorLabel->setText(m_currentColor);
        updatePreview();
    }
}

void BackgroundConfigDialog::onChooseColor2()
{
    QColor color = QColorDialog::getColor(QColor(m_currentColor2), this, "选择渐变颜色");
    if (color.isValid()) {
        m_currentColor2 = color.name();
        m_color2Btn->setStyleSheet(QString("background-color: %1; border: 1px solid #ccc;").arg(m_currentColor2));
        m_color2Label->setText(m_currentColor2);
        updatePreview();
    }
}

void BackgroundConfigDialog::onChooseImage()
{
    QString fileName = QFileDialog::getOpenFileName(this, "选择背景图片", 
                                                   "", 
                                                   "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)");
    if (!fileName.isEmpty()) {
        m_imagePathEdit->setText(fileName);
        m_currentImagePath = fileName;
        updatePreview();
    }
}

void BackgroundConfigDialog::onOpacityChanged(int value)
{
    m_currentOpacity = value / 100.0;
    m_opacityValueLabel->setText(QString("%1%").arg(value));
    updatePreview();
}

void BackgroundConfigDialog::onPreview()
{
    updatePreview();
}

void BackgroundConfigDialog::onResetToDefaults()
{
    loadConfig();
}

void BackgroundConfigDialog::updatePreview()
{
    QString style;
    
    if (m_colorRadio->isChecked()) {
        style = QString("background-color: %1;").arg(m_currentColor);
    } else if (m_gradientRadio->isChecked()) {
        style = QString("background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 %1, stop:1 %2);")
                .arg(m_currentColor, m_currentColor2);
    } else if (m_imageRadio->isChecked() && !m_currentImagePath.isEmpty()) {
        style = QString("background-image: url(%1); background-repeat: no-repeat; background-position: center;")
                .arg(m_currentImagePath);
    }
    
    if (m_currentOpacity < 1.0) {
        style += QString(" opacity: %1;").arg(m_currentOpacity);
    }
    
    m_previewLabel->setStyleSheet(style + " border: 1px solid #ccc;");
}

void BackgroundConfigDialog::accept()
{
    saveConfig();
    QDialog::accept();
}

void BackgroundConfigDialog::reject()
{
    QDialog::reject();
}

void BackgroundConfigDialog::saveConfig()
{
    QString type = "color";
    if (m_gradientRadio->isChecked()) type = "gradient";
    else if (m_imageRadio->isChecked()) type = "image";
    
    emit configChanged("background_type", type);
    emit configChanged("background_color", m_currentColor);
    emit configChanged("background_color2", m_currentColor2);
    emit configChanged("background_image", m_currentImagePath);
    emit configChanged("background_opacity", m_currentOpacity);
}

void BackgroundConfigDialog::setConfig(const QVariantMap &config)
{
    m_config = config;
    updateUI();
}

QVariantMap BackgroundConfigDialog::getConfig() const
{
    return m_config;
}

void BackgroundConfigDialog::updateUI()
{
    // 根据配置更新UI
    // 这里可以添加从配置加载UI状态的代码
}
