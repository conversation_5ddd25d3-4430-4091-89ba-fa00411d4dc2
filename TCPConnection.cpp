#include "TCPConnection.h"
#include <QMutexLocker>
#include <QDebug>
#include <QTextCodec>
#include <QHostInfo>

TCPConnection::TCPConnection(QObject *parent)
    : QObject(parent), m_socket(nullptr), m_host("localhost"), m_port(8080), m_protocol(TCP), m_state(Disconnected), m_isConnected(false), m_connectionTimeout(30), m_autoReconnectEnabled(false), m_keepAliveEnabled(false), m_keepAliveInterval(60), m_lineEnding("\r\n"), m_encoding("UTF-8"), m_bytesReceived(0), m_bytesSent(0), m_commandCount(0), m_isSending(false)
{
    // 创建TCP套接字
    m_socket = new QTcpSocket(this);
    QObject::connect(m_socket, &QTcpSocket::connected, this, &TCPConnection::onSocketConnected);
    QObject::connect(m_socket, &QTcpSocket::disconnected, this, &TCPConnection::onSocketDisconnected);
    QObject::connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
                     this, &TCPConnection::onSocketError);
    QObject::connect(m_socket, &QTcpSocket::readyRead, this, &TCPConnection::onSocketReadyRead);

    // 创建定时器
    m_connectionTimer = new QTimer(this);
    m_connectionTimer->setSingleShot(true);
    QObject::connect(m_connectionTimer, &QTimer::timeout, this, &TCPConnection::onConnectionTimeout);

    m_keepAliveTimer = new QTimer(this);
    QObject::connect(m_keepAliveTimer, &QTimer::timeout, this, &TCPConnection::onKeepAliveTimer);
}

TCPConnection::~TCPConnection()
{
    disconnect();
}

bool TCPConnection::connect(const QVariantMap &params)
{
    QMutexLocker locker(&m_mutex);

    if (m_isConnected || m_state == Connecting)
    {
        return false;
    }

    // 解析连接参数
    m_host = params.value("host", m_host).toString();
    m_port = params.value("port", m_port).toUInt();
    m_protocol = static_cast<Protocol>(params.value("protocol", TCP).toInt());
    m_connectionTimeout = params.value("timeout", m_connectionTimeout).toInt();
    m_autoReconnectEnabled = params.value("auto_reconnect", m_autoReconnectEnabled).toBool();
    m_keepAliveEnabled = params.value("keep_alive", m_keepAliveEnabled).toBool();
    m_lineEnding = params.value("line_ending", m_lineEnding).toString();
    m_encoding = params.value("encoding", m_encoding).toString();

    if (m_host.isEmpty() || m_port == 0)
    {
        emit errorOccurred("主机地址或端口无效");
        return false;
    }

    setState(Connecting);

    // 启动连接超时定时器
    m_connectionTimer->start(m_connectionTimeout * 1000);

    // 开始连接
    m_socket->connectToHost(m_host, m_port);

    return true;
}

void TCPConnection::disconnect()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isConnected && m_state == Disconnected)
    {
        return;
    }

    cleanup();

    if (m_socket && m_socket->state() != QAbstractSocket::UnconnectedState)
    {
        m_socket->disconnectFromHost();
        if (m_socket->state() != QAbstractSocket::UnconnectedState)
        {
            m_socket->waitForDisconnected(3000);
        }
    }

    setState(Disconnected);
    m_isConnected = false;

    emit disconnected();
}

bool TCPConnection::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_isConnected && m_socket && m_socket->state() == QAbstractSocket::ConnectedState;
}

TCPConnection::ConnectionState TCPConnection::state() const
{
    QMutexLocker locker(&m_mutex);
    return m_state;
}

QString TCPConnection::stateString() const
{
    switch (state())
    {
    case Disconnected:
        return "未连接";
    case Connecting:
        return "连接中";
    case Connected:
        return "已连接";
    case Error:
        return "错误";
    default:
        return "未知状态";
    }
}

bool TCPConnection::sendCommand(const QString &command)
{
    QString fullCommand = command + m_lineEnding;
    return sendData(fullCommand.toUtf8());
}

bool TCPConnection::sendData(const QByteArray &data)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected())
    {
        return false;
    }

    m_sendQueue.enqueue(data);

    if (!m_isSending)
    {
        m_isSending = true;

        while (!m_sendQueue.isEmpty())
        {
            QByteArray dataToSend = m_sendQueue.dequeue();

            qint64 bytesWritten = m_socket->write(dataToSend);
            if (bytesWritten == -1)
            {
                m_isSending = false;
                emit errorOccurred("数据发送失败");
                return false;
            }

            m_bytesSent += bytesWritten;
            m_commandCount++;
        }

        m_socket->flush();
        m_isSending = false;
    }

    return true;
}

void TCPConnection::setHost(const QString &host)
{
    QMutexLocker locker(&m_mutex);
    m_host = host;
}

QString TCPConnection::host() const
{
    QMutexLocker locker(&m_mutex);
    return m_host;
}

void TCPConnection::setPort(quint16 port)
{
    QMutexLocker locker(&m_mutex);
    m_port = port;
}

quint16 TCPConnection::port() const
{
    QMutexLocker locker(&m_mutex);
    return m_port;
}

void TCPConnection::setProtocol(Protocol protocol)
{
    QMutexLocker locker(&m_mutex);
    m_protocol = protocol;
}

TCPConnection::Protocol TCPConnection::protocol() const
{
    QMutexLocker locker(&m_mutex);
    return m_protocol;
}

void TCPConnection::setConnectionTimeout(int seconds)
{
    m_connectionTimeout = qMax(1, seconds);
}

int TCPConnection::connectionTimeout() const
{
    return m_connectionTimeout;
}

void TCPConnection::setAutoReconnect(bool enabled)
{
    m_autoReconnectEnabled = enabled;
}

bool TCPConnection::autoReconnectEnabled() const
{
    return m_autoReconnectEnabled;
}

void TCPConnection::setKeepAlive(bool enabled)
{
    m_keepAliveEnabled = enabled;
    if (enabled && m_isConnected)
    {
        m_keepAliveTimer->start(m_keepAliveInterval * 1000);
    }
    else
    {
        m_keepAliveTimer->stop();
    }
}

bool TCPConnection::keepAliveEnabled() const
{
    return m_keepAliveEnabled;
}

void TCPConnection::setLineEnding(const QString &ending)
{
    QMutexLocker locker(&m_mutex);
    m_lineEnding = ending;
}

QString TCPConnection::lineEnding() const
{
    QMutexLocker locker(&m_mutex);
    return m_lineEnding;
}

void TCPConnection::setEncoding(const QString &encoding)
{
    QMutexLocker locker(&m_mutex);
    m_encoding = encoding;
}

QString TCPConnection::encoding() const
{
    QMutexLocker locker(&m_mutex);
    return m_encoding;
}

qint64 TCPConnection::bytesReceived() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesReceived;
}

qint64 TCPConnection::bytesSent() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesSent;
}

QDateTime TCPConnection::connectionTime() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionTime;
}

int TCPConnection::commandCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_commandCount;
}

void TCPConnection::reconnect()
{
    disconnect();

    QVariantMap params;
    params["host"] = m_host;
    params["port"] = m_port;
    params["protocol"] = static_cast<int>(m_protocol);
    params["timeout"] = m_connectionTimeout;
    params["auto_reconnect"] = m_autoReconnectEnabled;
    params["keep_alive"] = m_keepAliveEnabled;
    params["line_ending"] = m_lineEnding;
    params["encoding"] = m_encoding;

    connect(params);
}

// 私有槽函数实现
void TCPConnection::onSocketConnected()
{
    QMutexLocker locker(&m_mutex);

    m_connectionTimer->stop();
    setState(Connected);
    m_isConnected = true;
    m_connectionTime = QDateTime::currentDateTime();

    // 启动保活定时器
    if (m_keepAliveEnabled)
    {
        m_keepAliveTimer->start(m_keepAliveInterval * 1000);
    }

    emit connected();
}

void TCPConnection::onSocketDisconnected()
{
    QMutexLocker locker(&m_mutex);

    bool wasConnected = m_isConnected;
    setState(Disconnected);
    m_isConnected = false;

    cleanup();

    if (wasConnected)
    {
        emit disconnected();

        // 自动重连
        if (m_autoReconnectEnabled)
        {
            QTimer::singleShot(2000, this, &TCPConnection::reconnect);
        }
    }
}

void TCPConnection::onSocketError(QAbstractSocket::SocketError error)
{
    QMutexLocker locker(&m_mutex);

    QString errorString;
    switch (error)
    {
    case QAbstractSocket::ConnectionRefusedError:
        errorString = "连接被拒绝";
        break;
    case QAbstractSocket::RemoteHostClosedError:
        errorString = "远程主机关闭连接";
        break;
    case QAbstractSocket::HostNotFoundError:
        errorString = "主机未找到";
        break;
    case QAbstractSocket::SocketAccessError:
        errorString = "套接字访问错误";
        break;
    case QAbstractSocket::SocketResourceError:
        errorString = "套接字资源错误";
        break;
    case QAbstractSocket::SocketTimeoutError:
        errorString = "套接字超时";
        break;
    case QAbstractSocket::NetworkError:
        errorString = "网络错误";
        break;
    case QAbstractSocket::UnsupportedSocketOperationError:
        errorString = "不支持的套接字操作";
        break;
    default:
        errorString = QString("未知错误 (%1)").arg(static_cast<int>(error));
        break;
    }

    setState(Error);
    emit errorOccurred(formatError(errorString));
}

void TCPConnection::onSocketReadyRead()
{
    processReceivedData();
}

void TCPConnection::onConnectionTimeout()
{
    QMutexLocker locker(&m_mutex);

    if (m_state == Connecting)
    {
        setState(Error);
        emit errorOccurred("连接超时");
    }
}

void TCPConnection::onKeepAliveTimer()
{
    if (m_isConnected && m_keepAliveEnabled)
    {
        // 发送保活数据（可以是空数据或特定的保活命令）
        sendData(QByteArray("\0", 1));
    }
}

// 私有方法实现
void TCPConnection::setState(ConnectionState state)
{
    if (m_state != state)
    {
        m_state = state;
        emit stateChanged(state);
    }
}

void TCPConnection::processReceivedData()
{
    QByteArray data = m_socket->readAll();
    if (!data.isEmpty())
    {
        m_receiveBuffer.append(data);
        m_bytesReceived += data.size();
        emit dataReceived(data);
    }
}

QString TCPConnection::formatError(const QString &error) const
{
    return QString("TCP连接错误 [%1:%2]: %3").arg(m_host).arg(m_port).arg(error);
}

void TCPConnection::cleanup()
{
    m_connectionTimer->stop();
    m_keepAliveTimer->stop();
    m_receiveBuffer.clear();
    m_sendQueue.clear();
    m_isSending = false;
}
