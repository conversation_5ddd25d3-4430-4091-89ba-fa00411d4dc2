#ifndef LOGMANAGER_H
#define LOGMANAGER_H

#include <QObject>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QTimer>
#include <QMutex>
#include <QVariantMap>
#include <QStringList>

class LogManager : public QObject
{
    Q_OBJECT

public:
    enum LogLevel {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        System = 4,
        Input = 5,
        Output = 6
    };
    Q_ENUM(LogLevel)

    explicit LogManager(QObject *parent = nullptr);
    ~LogManager();

    // 日志记录
    void log(const QString &message, LogLevel level = Info);
    void logDebug(const QString &message);
    void logInfo(const QString &message);
    void logWarning(const QString &message);
    void logError(const QString &message);
    void logSystem(const QString &message);
    void logInput(const QString &message);
    void logOutput(const QString &message);

    // 配置管理
    void setConfig(const QVariantMap &config);
    QVariantMap getConfig() const;
    void loadConfig();
    void saveConfig();

    // 文件管理
    void setLogFile(const QString &filePath);
    QString logFile() const;
    bool isLogFileEnabled() const;
    void setLogFileEnabled(bool enabled);

    // 时间戳配置
    void setTimestampEnabled(bool enabled);
    bool isTimestampEnabled() const;
    void setTimestampFormat(const QString &format);
    QString timestampFormat() const;
    QString currentTimestampFormat() const;

    // 回显配置
    void setEchoEnabled(bool enabled);
    bool isEchoEnabled() const;

    // 自动保存配置
    void setAutoSaveEnabled(bool enabled);
    bool isAutoSaveEnabled() const;
    void setAutoSaveInterval(int seconds);
    int autoSaveInterval() const;

    // 日志级别过滤
    void setLogLevel(LogLevel level);
    LogLevel logLevel() const;
    void setLevelEnabled(LogLevel level, bool enabled);
    bool isLevelEnabled(LogLevel level) const;

    // 文件轮转配置
    void setMaxFileSize(qint64 bytes);
    qint64 maxFileSize() const;
    void setMaxBackupFiles(int count);
    int maxBackupFiles() const;

    // 日志统计
    struct LogStats {
        QString filePath;
        bool fileExists;
        qint64 fileSize;
        int lineCount;
        QDateTime lastModified;
        QDateTime createdTime;
    };
    LogStats getLogStats() const;

    // 日志操作
    void clearLogs();
    bool exportLogs(const QString &filePath);
    QStringList getRecentLogs(int count = 100) const;

    // 格式化
    QString formatLogMessage(const QString &message, LogLevel level) const;
    QString levelToString(LogLevel level) const;
    LogLevel stringToLevel(const QString &levelStr) const;

public slots:
    void flush();
    void rotateLogs();

signals:
    void logSaved(const QString &message);
    void logError(const QString &error);
    void configChanged(const QString &key, const QVariant &value);
    void fileRotated(const QString &oldFile, const QString &newFile);

private slots:
    void onAutoSaveTimer();
    void checkFileSize();

private:
    void initializeConfig();
    void setupAutoSave();
    void writeToFile(const QString &formattedMessage);
    void rotateLogFile();
    QString generateTimestamp() const;
    QString getDefaultTimestampFormat(const QString &formatName) const;
    bool shouldLogLevel(LogLevel level) const;
    void ensureLogDirectory();

private:
    // 配置参数
    QVariantMap m_config;
    
    // 文件管理
    QString m_logFilePath;
    QFile *m_logFile;
    QTextStream *m_logStream;
    bool m_logFileEnabled;
    
    // 时间戳配置
    bool m_timestampEnabled;
    QString m_timestampFormat;
    QString m_customTimestampFormat;
    
    // 回显配置
    bool m_echoEnabled;
    
    // 自动保存
    bool m_autoSaveEnabled;
    int m_autoSaveInterval;
    QTimer *m_autoSaveTimer;
    
    // 日志级别
    LogLevel m_currentLogLevel;
    QMap<LogLevel, bool> m_levelEnabled;
    
    // 文件轮转
    qint64 m_maxFileSize;
    int m_maxBackupFiles;
    
    // 缓冲区
    QStringList m_logBuffer;
    int m_maxBufferSize;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 统计信息
    int m_totalLogCount;
    QDateTime m_sessionStartTime;
    
    // 预定义格式
    QMap<QString, QString> m_timestampFormats;
};

#endif // LOGMANAGER_H
