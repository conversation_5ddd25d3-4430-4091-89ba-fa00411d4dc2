#include "MainWindow.h"
#include "ConnectionManager.h"
#include "ConfigManager.h"
#include "LogManager.h"
#include "CommandHistory.h"
#include "InteractiveTerminal.h"
#include "CommandListWidget.h"
#include "ConnectionDialog.h"
#include "LogConfigDialog.h"
#include "BackgroundConfigDialog.h"

#include <QApplication>
#include <QMessageBox>
#include <QFileDialog>
#include <QInputDialog>
#include <QSplitter>
#include <QGroupBox>
#include <QFormLayout>
#include <QSpacerItem>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDesktopServices>
#include <QUrl>
#include <QSettings>
#include <QTimer>
#include <QMenu>
#include <QAction>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), m_connectionManager(nullptr), m_configManager(nullptr), m_logManager(nullptr), m_commandHistory(nullptr), m_centralWidget(nullptr), m_mainSplitter(nullptr), m_historyFrame(nullptr), m_mainWorkFrame(nullptr), m_commandsFrame(nullptr), m_menuBar(nullptr), m_toolBar(nullptr), m_statusBar(nullptr), m_topMenuFrame(nullptr), m_menuTitle(nullptr), m_configBtn(nullptr), m_importExportBtn(nullptr), m_helpBtn(nullptr), m_quickCommandFrame(nullptr), m_cmdBtn1(nullptr), m_cmdBtn2(nullptr), m_cmdBtn3(nullptr), m_quickCmdInput(nullptr), m_logFrame(nullptr), m_terminal(nullptr), m_commandInputFrame(nullptr), m_statusLabel(nullptr), m_commandInput(nullptr), m_sendBtn(nullptr), m_connectionTypeLabel(nullptr), m_connectionBtn(nullptr), m_commandsTitle(nullptr), m_toggleCommandsBtn(nullptr), m_commandsList(nullptr), m_addCommandBtn(nullptr), m_statusInfo(nullptr), m_versionLabel(nullptr), m_connectionDialog(nullptr), m_logConfigDialog(nullptr), m_backgroundConfigDialog(nullptr), m_commandsPanelVisible(true), m_isConnected(false), m_connectionType("未连接"), m_autoSaveTimer(nullptr), m_statusUpdateTimer(nullptr), m_settings(nullptr)
{
    qDebug() << "MainWindow constructor started";

    // 初始化核心组件
    qDebug() << "Creating core components...";
    m_configManager = new ConfigManager(this);
    m_logManager = new LogManager(this);
    m_commandHistory = new CommandHistory(this);
    m_connectionManager = new ConnectionManager(this);

    // 初始化设置
    m_settings = new QSettings(this);

    // 设置窗口属性
    qDebug() << "Setting window properties...";
    setWindowTitle("RF调试工具 v2.0 - Qt版本");
    setMinimumSize(1200, 800);

    // 加载窗口配置
    qDebug() << "Loading window config...";
    loadWindowConfig();

    // 设置UI
    qDebug() << "Setting up UI...";
    setupUI();
    qDebug() << "Setting up connections...";
    setupConnections();

    // 应用配置
    applyConfig();
    applyMacStyle();

    // 加载常用命令
    loadCommonCommands();

    // 设置定时器
    m_autoSaveTimer = new QTimer(this);
    m_autoSaveTimer->setInterval(30000); // 30秒自动保存
    connect(m_autoSaveTimer, &QTimer::timeout, this, &MainWindow::autoSave);
    m_autoSaveTimer->start();

    m_statusUpdateTimer = new QTimer(this);
    m_statusUpdateTimer->setInterval(1000); // 1秒更新状态
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &MainWindow::updateConnectionStatus);
    m_statusUpdateTimer->start();

    // 设置初始状态
    updateConnectionStatus();
    updateCommandButtons();

    // 显示欢迎信息
    qDebug() << "Adding welcome messages to terminal...";
    if (m_terminal)
    {
        m_terminal->appendMessage("=== RF调试工具 Qt版本 ===", LogManager::Info);
        m_terminal->appendMessage("", LogManager::Info);
        m_terminal->appendMessage("✅ 功能特性：", LogManager::Info);
        m_terminal->appendMessage("• 多种连接方式：ADB、串口、SSH、网络、FTP", LogManager::Info);
        m_terminal->appendMessage("• 串口高波特率支持：9600-3,000,000", LogManager::Info);
        m_terminal->appendMessage("• 智能日志管理：时间戳、存储、回显配置", LogManager::Info);
        m_terminal->appendMessage("• 背景配置：颜色、图片、透明度（精度0.01）", LogManager::Info);
        m_terminal->appendMessage("• 命令管理：右键菜单、启用/禁用、面板开关", LogManager::Info);
        m_terminal->appendMessage("• Mac风格界面：毛玻璃效果、现代化设计", LogManager::Info);
        m_terminal->appendMessage("", LogManager::Info);
        m_terminal->appendMessage("🚀 开始使用：点击'连接设备'按钮配置连接", LogManager::Info);
        m_terminal->appendMessage("", LogManager::Info);
    }
    else
    {
        qDebug() << "ERROR: m_terminal is null!";
    }

    qDebug() << "MainWindow constructor completed successfully";
}

MainWindow::~MainWindow()
{
    // 保存配置
    saveWindowConfig();
    saveCommonCommands();

    // 断开连接
    if (m_isConnected && m_connectionManager)
    {
        m_connectionManager->disconnect();
    }
}

void MainWindow::setupUI()
{
    // 创建中央部件
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);

    // 创建各个区域
    createTopMenuArea();
    createMainContentArea();
    createBottomStatusArea();

    // 添加到主布局
    mainLayout->addWidget(m_topMenuFrame);
    mainLayout->addWidget(m_mainSplitter, 1);

    // 设置状态栏
    setupStatusBar();
}

void MainWindow::createTopMenuArea()
{
    m_topMenuFrame = new QFrame;
    m_topMenuFrame->setObjectName("menuFrame");
    m_topMenuFrame->setFixedHeight(60);

    QHBoxLayout *menuLayout = new QHBoxLayout(m_topMenuFrame);
    menuLayout->setContentsMargins(20, 10, 20, 10);

    // 菜单标题
    m_menuTitle = new QLabel("RF调试工具");
    m_menuTitle->setObjectName("menuTitle");
    m_menuTitle->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);

    // 菜单按钮
    m_configBtn = new QPushButton("配置");
    m_configBtn->setObjectName("menuButton");

    m_importExportBtn = new QPushButton("导入/导出");
    m_importExportBtn->setObjectName("menuButton");

    m_helpBtn = new QPushButton("帮助");
    m_helpBtn->setObjectName("menuButton");

    menuLayout->addWidget(m_menuTitle);
    menuLayout->addStretch();
    menuLayout->addWidget(m_configBtn);
    menuLayout->addWidget(m_importExportBtn);
    menuLayout->addWidget(m_helpBtn);
}

void MainWindow::createMainContentArea()
{
    m_mainSplitter = new QSplitter(Qt::Horizontal);

    // 创建历史连接面板
    createHistoryPanel();

    // 创建主工作区域
    createMainWorkArea();

    // 创建常用命令面板
    createCommandsPanel();

    // 添加到分割器
    m_mainSplitter->addWidget(m_historyFrame);
    m_mainSplitter->addWidget(m_mainWorkFrame);
    m_mainSplitter->addWidget(m_commandsFrame);

    // 设置分割器比例
    m_mainSplitter->setStretchFactor(0, 0); // 历史面板固定宽度
    m_mainSplitter->setStretchFactor(1, 1); // 主工作区域可伸缩
    m_mainSplitter->setStretchFactor(2, 0); // 命令面板固定宽度

    // 设置初始大小
    m_mainSplitter->setSizes({80, 800, 150});
}

void MainWindow::createHistoryPanel()
{
    m_historyFrame = new QFrame;
    m_historyFrame->setObjectName("historyFrame");
    m_historyFrame->setFixedWidth(80);

    QVBoxLayout *historyLayout = new QVBoxLayout(m_historyFrame);
    historyLayout->setContentsMargins(10, 20, 10, 20);
    historyLayout->setSpacing(10);

    QLabel *historyTitle = new QLabel("历\n史\n连\n接\n，\n可\n手\n动\n隐\n藏");
    historyTitle->setObjectName("historyTitle");
    historyTitle->setAlignment(Qt::AlignCenter);
    historyTitle->setWordWrap(true);

    historyLayout->addWidget(historyTitle);
    historyLayout->addStretch();
}

void MainWindow::createMainWorkArea()
{
    m_mainWorkFrame = new QFrame;
    m_mainWorkFrame->setObjectName("mainWorkArea");

    QVBoxLayout *mainAreaLayout = new QVBoxLayout(m_mainWorkFrame);
    mainAreaLayout->setContentsMargins(20, 20, 20, 20);
    mainAreaLayout->setSpacing(15);

    // 创建各个子区域
    createQuickCommandsArea();
    createLogDisplayArea();
    createCommandInputArea();

    // 添加到布局
    mainAreaLayout->addWidget(m_quickCommandFrame);
    mainAreaLayout->addWidget(m_logFrame, 1);
    mainAreaLayout->addWidget(m_commandInputFrame);
}

void MainWindow::createQuickCommandsArea()
{
    m_quickCommandFrame = new QFrame;
    m_quickCommandFrame->setObjectName("quickCommandFrame");
    m_quickCommandFrame->setFixedHeight(80);

    QHBoxLayout *quickCmdLayout = new QHBoxLayout(m_quickCommandFrame);
    quickCmdLayout->setContentsMargins(20, 15, 20, 15);
    quickCmdLayout->setSpacing(15);

    // 快捷命令按钮
    m_cmdBtn1 = new QPushButton("命令1");
    m_cmdBtn1->setObjectName("quickCommandBtn");
    m_cmdBtn1->setFixedSize(80, 40);

    m_cmdBtn2 = new QPushButton("命令2");
    m_cmdBtn2->setObjectName("quickCommandBtn");
    m_cmdBtn2->setFixedSize(80, 40);

    m_cmdBtn3 = new QPushButton("命令3");
    m_cmdBtn3->setObjectName("quickCommandBtn");
    m_cmdBtn3->setFixedSize(80, 40);

    // 快捷命令输入框
    m_quickCmdInput = new QLineEdit;
    m_quickCmdInput->setPlaceholderText("快捷命令框");
    m_quickCmdInput->setObjectName("quickCommandInput");
    m_quickCmdInput->setFixedHeight(40);

    quickCmdLayout->addWidget(m_cmdBtn1);
    quickCmdLayout->addWidget(m_cmdBtn2);
    quickCmdLayout->addWidget(m_cmdBtn3);
    quickCmdLayout->addWidget(m_quickCmdInput);
}

void MainWindow::createLogDisplayArea()
{
    qDebug() << "Creating log display area...";

    m_logFrame = new QFrame;
    m_logFrame->setObjectName("logFrame");

    QVBoxLayout *logLayout = new QVBoxLayout(m_logFrame);
    logLayout->setContentsMargins(20, 20, 20, 20);

    // 创建交互式终端
    qDebug() << "Creating InteractiveTerminal...";
    m_terminal = new InteractiveTerminal;
    m_terminal->setObjectName("logDisplay");
    m_terminal->setPlaceholderText("日志显示区域 - 支持命令输入和历史记录");

    logLayout->addWidget(m_terminal);

    qDebug() << "Log display area created successfully, m_terminal:" << m_terminal;
}

void MainWindow::createCommandInputArea()
{
    m_commandInputFrame = new QFrame;
    m_commandInputFrame->setObjectName("commandInputFrame");
    m_commandInputFrame->setFixedHeight(60);

    QHBoxLayout *inputLayout = new QHBoxLayout(m_commandInputFrame);
    inputLayout->setContentsMargins(20, 10, 20, 10);
    inputLayout->setSpacing(15);

    // 状态标签
    m_statusLabel = new QLabel("状态栏");
    m_statusLabel->setObjectName("statusLabel");

    // 命令输入框
    m_commandInput = new QLineEdit;
    m_commandInput->setPlaceholderText("用户命令发送框");
    m_commandInput->setObjectName("commandInput");
    m_commandInput->setFixedHeight(35);

    // 发送按钮
    m_sendBtn = new QPushButton("发送");
    m_sendBtn->setObjectName("sendButton");
    m_sendBtn->setFixedSize(60, 35);

    // 连接状态标签
    m_connectionTypeLabel = new QLabel("未连接");
    m_connectionTypeLabel->setObjectName("connectionTypeLabel");
    m_connectionTypeLabel->setFixedHeight(35);

    // 连接按钮
    m_connectionBtn = new QPushButton("连接设备");
    m_connectionBtn->setObjectName("connectionButton");
    m_connectionBtn->setFixedSize(100, 35);

    inputLayout->addWidget(m_statusLabel);
    inputLayout->addWidget(m_commandInput);
    inputLayout->addWidget(m_sendBtn);
    inputLayout->addWidget(m_connectionTypeLabel);
    inputLayout->addWidget(m_connectionBtn);
}

void MainWindow::createCommandsPanel()
{
    m_commandsFrame = new QFrame;
    m_commandsFrame->setObjectName("commandsFrame");
    m_commandsFrame->setFixedWidth(150);

    QVBoxLayout *commandsLayout = new QVBoxLayout(m_commandsFrame);
    commandsLayout->setContentsMargins(15, 20, 15, 20);
    commandsLayout->setSpacing(15);

    // 标题和控制按钮
    QHBoxLayout *titleLayout = new QHBoxLayout;
    m_commandsTitle = new QLabel("常用命令");
    m_commandsTitle->setObjectName("commandsTitle");
    m_commandsTitle->setAlignment(Qt::AlignCenter);

    m_toggleCommandsBtn = new QPushButton("−");
    m_toggleCommandsBtn->setObjectName("toggleButton");
    m_toggleCommandsBtn->setFixedSize(20, 20);

    titleLayout->addWidget(m_commandsTitle);
    titleLayout->addWidget(m_toggleCommandsBtn);

    // 命令列表
    m_commandsList = new CommandListWidget;
    m_commandsList->setObjectName("commandsList");

    // 添加命令按钮
    m_addCommandBtn = new QPushButton("+ 添加命令");
    m_addCommandBtn->setObjectName("addCommandBtn");

    commandsLayout->addLayout(titleLayout);
    commandsLayout->addWidget(m_commandsList);
    commandsLayout->addWidget(m_addCommandBtn);
}

void MainWindow::createBottomStatusArea()
{
    // 状态栏在setupStatusBar中创建
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();

    // 状态信息
    m_statusInfo = new QLabel("就绪");
    m_statusInfo->setObjectName("statusInfo");

    // 版本信息
    m_versionLabel = new QLabel("RF调试工具 v2.0 - Qt版本");
    m_versionLabel->setObjectName("versionLabel");

    m_statusBar->addWidget(m_statusInfo);
    m_statusBar->addPermanentWidget(m_versionLabel);
}

void MainWindow::setupConnections()
{
    // 菜单按钮连接
    connect(m_configBtn, &QPushButton::clicked, this, &MainWindow::showConfigMenu);
    connect(m_importExportBtn, &QPushButton::clicked, this, &MainWindow::showImportExportMenu);
    connect(m_helpBtn, &QPushButton::clicked, this, &MainWindow::showHelp);

    // 快捷命令按钮连接
    connect(m_cmdBtn1, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    connect(m_cmdBtn2, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    connect(m_cmdBtn3, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);

    // 命令输入连接
    connect(m_commandInput, &QLineEdit::returnPressed, this, &MainWindow::sendCommand);
    connect(m_quickCmdInput, &QLineEdit::returnPressed, this, &MainWindow::sendCommand);
    connect(m_sendBtn, &QPushButton::clicked, this, &MainWindow::sendCommand);

    // 连接按钮
    connect(m_connectionBtn, &QPushButton::clicked, this, &MainWindow::showConnectionDialog);

    // 命令面板
    connect(m_toggleCommandsBtn, &QPushButton::clicked, this, &MainWindow::toggleCommandsPanel);
    connect(m_addCommandBtn, &QPushButton::clicked, this, &MainWindow::addNewCommand);

    // 连接管理器信号
    if (m_connectionManager)
    {
        connect(m_connectionManager, &ConnectionManager::statusChanged,
                this, &MainWindow::onConnectionStatusChanged);
        connect(m_connectionManager, &ConnectionManager::dataReceived,
                this, &MainWindow::onDataReceived);
        connect(m_connectionManager, &ConnectionManager::errorOccurred,
                this, &MainWindow::onErrorOccurred);
    }

    // 配置管理器信号
    if (m_configManager)
    {
        connect(m_configManager, &ConfigManager::configChanged,
                this, &MainWindow::onConfigChanged);
    }

    // 命令列表信号
    if (m_commandsList)
    {
        connect(m_commandsList, &CommandListWidget::executeCommand,
                this, &MainWindow::executeCommonCommand);
        connect(m_commandsList, &CommandListWidget::commandEdited,
                this, &MainWindow::onCommandEdited);
        connect(m_commandsList, &CommandListWidget::commandDeleted,
                this, &MainWindow::onCommandDeleted);
        connect(m_commandsList, &CommandListWidget::commandAdded,
                this, &MainWindow::onCommandAdded);
    }

    // 终端信号
    if (m_terminal)
    {
        connect(m_terminal, &InteractiveTerminal::commandEntered,
                this, [this](const QString &command)
                {
                    if (m_connectionManager && m_isConnected) {
                        m_connectionManager->sendCommand(command);
                        m_commandHistory->addCommand(command);
                    } else {
                        m_terminal->appendMessage("警告: 设备未连接", LogManager::Warning);
                    } });
    }
}

// 连接相关槽函数
void MainWindow::showConnectionDialog()
{
    if (!m_connectionDialog)
    {
        m_connectionDialog = new ConnectionDialog(this);
        connect(m_connectionDialog, &ConnectionDialog::connectionRequested,
                this, &MainWindow::connectDevice);
        connect(m_connectionDialog, &ConnectionDialog::testConnectionRequested,
                m_connectionManager, &ConnectionManager::testConnection);
    }

    m_connectionDialog->show();
    m_connectionDialog->raise();
    m_connectionDialog->activateWindow();
}

void MainWindow::connectDevice()
{
    if (m_connectionDialog)
    {
        QVariantMap params = m_connectionDialog->getConnectionParams();
        if (m_connectionManager)
        {
            bool success = m_connectionManager->connectToDevice(params);
            if (success)
            {
                m_isConnected = true;
                m_connectionParams = params;
                m_connectionType = params.value("type", "未知").toString();
                updateConnectionStatus();
                updateCommandButtons();

                m_terminal->appendMessage("✅ 设备连接成功", LogManager::Info);
                m_connectionDialog->accept();
            }
            else
            {
                m_terminal->appendMessage("❌ 设备连接失败", LogManager::Error);
            }
        }
    }
}

void MainWindow::disconnectDevice()
{
    if (m_connectionManager && m_isConnected)
    {
        m_connectionManager->disconnectFromDevice();
        m_isConnected = false;
        m_connectionType = "未连接";
        m_connectionParams.clear();
        updateConnectionStatus();
        updateCommandButtons();

        m_terminal->appendMessage("🔌 设备已断开连接", LogManager::Info);
    }
}

void MainWindow::onConnectionStatusChanged(const QString &status, const QString &details)
{
    m_statusLabel->setText(status);
    if (!details.isEmpty())
    {
        m_terminal->appendMessage(details, LogManager::System);
    }

    // 更新连接状态
    if (status.contains("已连接"))
    {
        m_isConnected = true;
    }
    else if (status.contains("断开") || status.contains("失败"))
    {
        m_isConnected = false;
    }

    updateConnectionStatus();
    updateCommandButtons();
}

void MainWindow::onDataReceived(const QString &data)
{
    if (m_terminal)
    {
        m_terminal->appendSystemOutput(data);
    }
}

void MainWindow::onErrorOccurred(const QString &error)
{
    if (m_terminal)
    {
        m_terminal->appendError(error);
    }

    m_statusLabel->setText("错误: " + error);
}

// 命令相关槽函数
void MainWindow::sendCommand()
{
    QString command;

    // 确定命令来源
    QObject *sender = this->sender();
    if (sender == m_commandInput)
    {
        command = m_commandInput->text().trimmed();
        m_commandInput->clear();
    }
    else if (sender == m_quickCmdInput)
    {
        command = m_quickCmdInput->text().trimmed();
        m_quickCmdInput->clear();
    }
    else if (sender == m_sendBtn)
    {
        command = m_commandInput->text().trimmed();
        m_commandInput->clear();
    }

    if (command.isEmpty())
    {
        return;
    }

    if (!m_isConnected || !m_connectionManager)
    {
        m_terminal->appendMessage("警告: 设备未连接", LogManager::Warning);
        return;
    }

    // 发送命令
    bool success = m_connectionManager->sendCommand(command);
    if (success)
    {
        m_terminal->appendUserInput(command);
        m_commandHistory->addCommand(command);
    }
    else
    {
        m_terminal->appendError("命令发送失败");
    }
}

void MainWindow::executeQuickCommand()
{
    QPushButton *button = qobject_cast<QPushButton *>(sender());
    if (!button)
    {
        return;
    }

    QString command;
    if (button == m_cmdBtn1)
    {
        command = m_configManager->getValue("quick_command_1", "ls").toString();
    }
    else if (button == m_cmdBtn2)
    {
        command = m_configManager->getValue("quick_command_2", "pwd").toString();
    }
    else if (button == m_cmdBtn3)
    {
        command = m_configManager->getValue("quick_command_3", "whoami").toString();
    }

    if (!command.isEmpty())
    {
        if (m_isConnected && m_connectionManager)
        {
            bool success = m_connectionManager->sendCommand(command);
            if (success)
            {
                m_terminal->appendUserInput(command);
                m_commandHistory->addCommand(command);
            }
            else
            {
                m_terminal->appendError("命令发送失败");
            }
        }
        else
        {
            m_terminal->appendMessage("警告: 设备未连接", LogManager::Warning);
        }
    }
}

void MainWindow::executeCommonCommand(const QString &name, const QString &content)
{
    if (content.isEmpty())
    {
        return;
    }

    if (!m_isConnected || !m_connectionManager)
    {
        m_terminal->appendMessage("警告: 设备未连接", LogManager::Warning);
        return;
    }

    bool success = m_connectionManager->sendCommand(content);
    if (success)
    {
        m_terminal->appendUserInput(content);
        m_commandHistory->addCommand(content);
        m_terminal->appendMessage(QString("执行常用命令: %1").arg(name), LogManager::Info);
    }
    else
    {
        m_terminal->appendError("命令发送失败");
    }
}

void MainWindow::onCommandEdited(const QString &oldName, const QString &newName, const QString &newContent)
{
    // 更新配置中的命令
    if (m_configManager)
    {
        QVariantList commands = m_configManager->getCommands();
        for (int i = 0; i < commands.size(); ++i)
        {
            QVariantMap command = commands[i].toMap();
            if (command["name"].toString() == oldName)
            {
                command["name"] = newName;
                command["content"] = newContent;
                commands[i] = command;
                break;
            }
        }
        m_configManager->setCommands(commands);
    }

    m_terminal->appendMessage(QString("命令已更新: %1 -> %2").arg(oldName, newName), LogManager::Info);
}

void MainWindow::onCommandDeleted(const QString &name)
{
    // 从配置中删除命令
    if (m_configManager)
    {
        QVariantList commands = m_configManager->getCommands();
        for (int i = 0; i < commands.size(); ++i)
        {
            QVariantMap command = commands[i].toMap();
            if (command["name"].toString() == name)
            {
                commands.removeAt(i);
                break;
            }
        }
        m_configManager->setCommands(commands);
    }

    m_terminal->appendMessage(QString("命令已删除: %1").arg(name), LogManager::Info);
}

void MainWindow::onCommandAdded(const QString &name, const QString &content)
{
    // 添加到配置中
    if (m_configManager)
    {
        QVariantMap command;
        command["name"] = name;
        command["content"] = content;
        command["enabled"] = true;
        command["created"] = QDateTime::currentDateTime();

        m_configManager->addCommand(command);
    }

    m_terminal->appendMessage(QString("命令已添加: %1").arg(name), LogManager::Info);
}

void MainWindow::addNewCommand()
{
    bool ok;
    QString name = QInputDialog::getText(this, "添加命令", "命令名称:", QLineEdit::Normal, "", &ok);
    if (!ok || name.trimmed().isEmpty())
    {
        return;
    }

    QString content = QInputDialog::getMultiLineText(this, "添加命令", "命令内容:", "", &ok);
    if (!ok || content.trimmed().isEmpty())
    {
        return;
    }

    QString description = QInputDialog::getText(this, "添加命令", "命令描述 (可选):", QLineEdit::Normal, "", &ok);
    if (!ok)
    {
        description = "";
    }

    if (m_commandsList)
    {
        m_commandsList->addCommand(name.trimmed(), content.trimmed(), description.trimmed());
    }
}

// 配置相关槽函数
void MainWindow::showConfigMenu()
{
    QMenu menu(this);
    menu.addAction("日志配置", this, &MainWindow::showLogConfig);
    menu.addAction("背景配置", this, &MainWindow::showBackgroundConfig);
    menu.addAction("界面配置", this, &MainWindow::showUIConfig);
    menu.addAction("终端配置", this, &MainWindow::showTerminalConfig);
    menu.addSeparator();
    menu.addAction("重置配置", this, &MainWindow::resetConfig);

    menu.exec(m_configBtn->mapToGlobal(QPoint(0, m_configBtn->height())));
}

void MainWindow::showLogConfig()
{
    if (!m_logConfigDialog)
    {
        m_logConfigDialog = new LogConfigDialog(this);
        connect(m_logConfigDialog, &LogConfigDialog::configChanged,
                this, &MainWindow::onConfigChanged);
    }

    m_logConfigDialog->show();
    m_logConfigDialog->raise();
    m_logConfigDialog->activateWindow();
}

void MainWindow::showBackgroundConfig()
{
    if (!m_backgroundConfigDialog)
    {
        m_backgroundConfigDialog = new BackgroundConfigDialog(this);
        connect(m_backgroundConfigDialog, &BackgroundConfigDialog::configChanged,
                this, &MainWindow::onConfigChanged);
    }

    m_backgroundConfigDialog->show();
    m_backgroundConfigDialog->raise();
    m_backgroundConfigDialog->activateWindow();
}

void MainWindow::showUIConfig()
{
    QMessageBox::information(this, "界面配置", "界面配置功能正在开发中...");
}

void MainWindow::showTerminalConfig()
{
    QMessageBox::information(this, "终端配置", "终端配置功能正在开发中...");
}

void MainWindow::resetConfig()
{
    int ret = QMessageBox::question(this, "重置配置",
                                    "确定要重置所有配置到默认值吗？\n此操作不可撤销。",
                                    QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes)
    {
        if (m_configManager)
        {
            m_configManager->resetToDefaults();
            applyConfig();
            m_terminal->appendMessage("配置已重置到默认值", LogManager::Info);
        }
    }
}

void MainWindow::onConfigChanged(const QString &key, const QVariant &value)
{
    if (m_configManager)
    {
        m_configManager->setValue(key, value);
    }

    // 应用特定配置更改
    if (key.startsWith("background_"))
    {
        applyBackgroundConfig();
    }
    else if (key.startsWith("log_"))
    {
        // 应用日志配置
        if (m_logManager)
        {
            m_logManager->loadConfig();
        }
    }

    m_terminal->appendMessage(QString("配置已更新: %1").arg(key), LogManager::Debug);
}

// 导入导出相关槽函数
void MainWindow::showImportExportMenu()
{
    QMenu menu(this);
    menu.addAction("导出配置", this, &MainWindow::exportConfig);
    menu.addAction("导入配置", this, &MainWindow::importConfig);
    menu.addSeparator();
    menu.addAction("导出命令", this, &MainWindow::exportCommands);
    menu.addAction("导入命令", this, &MainWindow::importCommands);

    menu.exec(m_importExportBtn->mapToGlobal(QPoint(0, m_importExportBtn->height())));
}

void MainWindow::exportConfig()
{
    QString fileName = QFileDialog::getSaveFileName(this, "导出配置",
                                                    "rf_tool_config.json",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_configManager)
    {
        bool success = m_configManager->exportConfig(fileName);
        if (success)
        {
            m_terminal->appendMessage("配置导出成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("配置导出失败", LogManager::Error);
        }
    }
}

void MainWindow::importConfig()
{
    QString fileName = QFileDialog::getOpenFileName(this, "导入配置",
                                                    "",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_configManager)
    {
        bool success = m_configManager->importConfig(fileName);
        if (success)
        {
            applyConfig();
            m_terminal->appendMessage("配置导入成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("配置导入失败", LogManager::Error);
        }
    }
}

void MainWindow::exportCommands()
{
    QString fileName = QFileDialog::getSaveFileName(this, "导出命令",
                                                    "rf_tool_commands.json",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_commandsList)
    {
        bool success = m_commandsList->exportCommands(fileName);
        if (success)
        {
            m_terminal->appendMessage("命令导出成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("命令导出失败", LogManager::Error);
        }
    }
}

void MainWindow::importCommands()
{
    QString fileName = QFileDialog::getOpenFileName(this, "导入命令",
                                                    "",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_commandsList)
    {
        bool success = m_commandsList->importCommands(fileName);
        if (success)
        {
            m_terminal->appendMessage("命令导入成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("命令导入失败", LogManager::Error);
        }
    }
}

// 界面相关槽函数
void MainWindow::toggleCommandsPanel()
{
    m_commandsPanelVisible = !m_commandsPanelVisible;
    m_commandsFrame->setVisible(m_commandsPanelVisible);

    m_toggleCommandsBtn->setText(m_commandsPanelVisible ? "−" : "+");

    if (m_commandsPanelVisible)
    {
        m_terminal->appendMessage("常用命令面板已显示", LogManager::Debug);
    }
    else
    {
        m_terminal->appendMessage("常用命令面板已隐藏", LogManager::Debug);
    }
}

void MainWindow::updateConnectionStatus()
{
    if (m_isConnected)
    {
        m_connectionBtn->setText("断开连接");
        m_connectionTypeLabel->setText(m_connectionType);
        m_statusInfo->setText("已连接");

        // 更新按钮样式
        m_connectionBtn->setStyleSheet("QPushButton { background-color: #dc3545; }");
    }
    else
    {
        m_connectionBtn->setText("连接设备");
        m_connectionTypeLabel->setText("未连接");
        m_statusInfo->setText("未连接");

        // 恢复默认样式
        m_connectionBtn->setStyleSheet("");
    }
}

void MainWindow::updateCommandButtons()
{
    bool enabled = m_isConnected;

    m_sendBtn->setEnabled(enabled);
    m_commandInput->setEnabled(enabled);
    m_quickCmdInput->setEnabled(enabled);
    m_cmdBtn1->setEnabled(enabled);
    m_cmdBtn2->setEnabled(enabled);
    m_cmdBtn3->setEnabled(enabled);

    if (m_commandsList)
    {
        m_commandsList->setEnabled(enabled);
    }
}

void MainWindow::applyBackgroundConfig()
{
    if (!m_configManager)
    {
        return;
    }

    QVariantMap bgConfig;
    bgConfig["type"] = m_configManager->getValue("background_type", "color").toString();
    bgConfig["color"] = m_configManager->getValue("background_color", "#f8f9fa").toString();
    bgConfig["image"] = m_configManager->getValue("background_image", "").toString();
    bgConfig["opacity"] = m_configManager->getValue("background_opacity", 1.0).toDouble();

    QString bgStyle = buildBackgroundStyle(bgConfig);
    updateMainWindowStyle(bgStyle);
}

// 其他槽函数
void MainWindow::showHelp()
{
    QString helpText = R"(
RF调试工具 Qt版本 - 帮助文档

=== 主要功能 ===
• 多种连接方式：串口、SSH、ADB、网络、FTP
• 智能命令管理：常用命令、历史记录、快捷命令
• 高级日志功能：时间戳、存储、过滤
• 现代化界面：Mac风格、背景配置、主题切换

=== 快捷键 ===
• Enter: 发送命令
• Ctrl+L: 清空终端
• Ctrl+S: 保存配置
• F1: 显示帮助

=== 连接类型 ===
• 串口: 支持高波特率 (9600-3,000,000)
• SSH: 支持密钥和密码认证
• ADB: Android设备调试
• 网络: TCP/UDP连接
• FTP: 文件传输协议

=== 技术支持 ===
如有问题，请联系开发团队。
版本: 2.0.0
    )";

    QMessageBox::information(this, "帮助", helpText);
}

void MainWindow::autoSave()
{
    if (m_configManager)
    {
        m_configManager->save();
    }

    saveCommonCommands();
    saveWindowConfig();
}

// 私有方法实现
void MainWindow::applyMacStyle()
{
    // Mac风格样式已在main.cpp中设置
    // 这里可以添加额外的样式定制
}

void MainWindow::loadWindowConfig()
{
    if (!m_settings)
    {
        return;
    }

    // 恢复窗口几何
    QByteArray geometry = m_settings->value("window/geometry").toByteArray();
    if (!geometry.isEmpty())
    {
        restoreGeometry(geometry);
    }

    // 恢复窗口状态
    QByteArray state = m_settings->value("window/state").toByteArray();
    if (!state.isEmpty())
    {
        restoreState(state);
    }

    // 恢复分割器状态
    QByteArray splitterState = m_settings->value("window/splitter").toByteArray();
    if (!splitterState.isEmpty() && m_mainSplitter)
    {
        m_mainSplitter->restoreState(splitterState);
    }

    // 恢复命令面板可见性
    m_commandsPanelVisible = m_settings->value("window/commands_panel_visible", true).toBool();
}

void MainWindow::saveWindowConfig()
{
    if (!m_settings)
    {
        return;
    }

    // 保存窗口几何
    m_settings->setValue("window/geometry", saveGeometry());

    // 保存窗口状态
    m_settings->setValue("window/state", saveState());

    // 保存分割器状态
    if (m_mainSplitter)
    {
        m_settings->setValue("window/splitter", m_mainSplitter->saveState());
    }

    // 保存命令面板可见性
    m_settings->setValue("window/commands_panel_visible", m_commandsPanelVisible);

    m_settings->sync();
}

void MainWindow::loadCommonCommands()
{
    if (!m_configManager || !m_commandsList)
    {
        return;
    }

    QVariantList commands = m_configManager->getCommands();
    for (const QVariant &var : commands)
    {
        QVariantMap command = var.toMap();
        QString name = command["name"].toString();
        QString content = command["content"].toString();
        QString description = command["description"].toString();

        if (!name.isEmpty() && !content.isEmpty())
        {
            m_commandsList->addCommand(name, content, description);
        }
    }

    // 如果没有命令，添加一些默认命令
    if (commands.isEmpty())
    {
        m_commandsList->addCommand("列出文件", "ls -la", "显示当前目录的详细文件列表");
        m_commandsList->addCommand("当前目录", "pwd", "显示当前工作目录");
        m_commandsList->addCommand("系统信息", "uname -a", "显示系统信息");
        m_commandsList->addCommand("磁盘使用", "df -h", "显示磁盘使用情况");
        m_commandsList->addCommand("内存使用", "free -h", "显示内存使用情况");
    }
}

void MainWindow::saveCommonCommands()
{
    if (!m_configManager || !m_commandsList)
    {
        return;
    }

    QVariantList commands = m_commandsList->toVariantList();
    m_configManager->setCommands(commands);
}

void MainWindow::applyConfig()
{
    if (!m_configManager)
    {
        return;
    }

    // 应用背景配置
    applyBackgroundConfig();

    // 应用日志配置
    if (m_logManager)
    {
        QVariantMap logConfig;
        logConfig["timestamp_enabled"] = m_configManager->getValue("log_timestamp_enabled", true);
        logConfig["timestamp_format"] = m_configManager->getValue("log_timestamp_format", "yyyy-MM-dd hh:mm:ss");
        logConfig["echo_enabled"] = m_configManager->getValue("log_echo_enabled", true);
        logConfig["file_enabled"] = m_configManager->getValue("log_file_enabled", false);
        logConfig["file_path"] = m_configManager->getValue("log_file_path", "");

        m_logManager->setConfig(logConfig);
    }

    // 应用终端配置
    if (m_terminal)
    {
        bool timestampEnabled = m_configManager->getValue("terminal_timestamp_enabled", true).toBool();
        QString timestampFormat = m_configManager->getValue("terminal_timestamp_format", "hh:mm:ss").toString();
        bool echoEnabled = m_configManager->getValue("terminal_echo_enabled", true).toBool();

        m_terminal->setTimestampEnabled(timestampEnabled);
        m_terminal->setTimestampFormat(timestampFormat);
        m_terminal->setEchoEnabled(echoEnabled);
    }

    // 更新快捷命令按钮文本
    if (m_cmdBtn1)
    {
        QString cmd1Name = m_configManager->getValue("quick_command_1_name", "命令1").toString();
        m_cmdBtn1->setText(cmd1Name);
    }
    if (m_cmdBtn2)
    {
        QString cmd2Name = m_configManager->getValue("quick_command_2_name", "命令2").toString();
        m_cmdBtn2->setText(cmd2Name);
    }
    if (m_cmdBtn3)
    {
        QString cmd3Name = m_configManager->getValue("quick_command_3_name", "命令3").toString();
        m_cmdBtn3->setText(cmd3Name);
    }
}

QString MainWindow::buildBackgroundStyle(const QVariantMap &config)
{
    QString type = config.value("type", "color").toString();
    QString color = config.value("color", "#f8f9fa").toString();
    QString image = config.value("image", "").toString();
    double opacity = config.value("opacity", 1.0).toDouble();

    QString style;

    if (type == "color")
    {
        style = QString("QMainWindow { background-color: %1; }").arg(color);
    }
    else if (type == "image" && !image.isEmpty())
    {
        style = QString("QMainWindow { background-image: url(%1); background-repeat: no-repeat; background-position: center; }").arg(image);
    }
    else if (type == "gradient")
    {
        QString color2 = config.value("color2", "#e9ecef").toString();
        style = QString("QMainWindow { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 %1, stop:1 %2); }").arg(color, color2);
    }

    // 应用透明度
    if (opacity < 1.0)
    {
        style += QString("QMainWindow { opacity: %1; }").arg(opacity);
    }

    return style;
}

void MainWindow::updateMainWindowStyle(const QString &bgStyle)
{
    if (!bgStyle.isEmpty())
    {
        setStyleSheet(bgStyle);
    }
}

// 事件处理函数实现
void MainWindow::closeEvent(QCloseEvent *event)
{
    // 保存配置
    saveWindowConfig();
    saveCommonCommands();

    // 断开连接
    if (m_isConnected && m_connectionManager)
    {
        m_connectionManager->disconnectFromDevice();
    }

    // 接受关闭事件
    event->accept();
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 可以在这里添加窗口大小改变时的处理逻辑
    if (m_settings)
    {
        m_settings->setValue("window/size", event->size());
    }
}

void MainWindow::showEvent(QShowEvent *event)
{
    QMainWindow::showEvent(event);

    // 窗口显示时的处理逻辑
    if (m_terminal)
    {
        m_terminal->appendMessage("窗口已显示", LogManager::Debug);
    }
}
