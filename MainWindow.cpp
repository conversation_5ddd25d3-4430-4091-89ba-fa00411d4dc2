#include "MainWindow.h"
#include "ConnectionManager.h"
#include "ConfigManager.h"
#include "LogManager.h"
#include "CommandHistory.h"
#include "InteractiveTerminal.h"
#include "CommandListWidget.h"
#include "ConnectionDialog.h"
#include "LogConfigDialog.h"
#include "BackgroundConfigDialog.h"

#include <QApplication>
#include <QMessageBox>
#include <QFileDialog>
#include <QInputDialog>
#include <QSplitter>
#include <QGroupBox>
#include <QFormLayout>
#include <QSpacerItem>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDesktopServices>
#include <QUrl>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_connectionManager(nullptr)
    , m_configManager(nullptr)
    , m_logManager(nullptr)
    , m_commandHistory(nullptr)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_historyFrame(nullptr)
    , m_mainWorkFrame(nullptr)
    , m_commandsFrame(nullptr)
    , m_menuBar(nullptr)
    , m_toolBar(nullptr)
    , m_statusBar(nullptr)
    , m_topMenuFrame(nullptr)
    , m_menuTitle(nullptr)
    , m_configBtn(nullptr)
    , m_importExportBtn(nullptr)
    , m_helpBtn(nullptr)
    , m_quickCommandFrame(nullptr)
    , m_cmdBtn1(nullptr)
    , m_cmdBtn2(nullptr)
    , m_cmdBtn3(nullptr)
    , m_quickCmdInput(nullptr)
    , m_logFrame(nullptr)
    , m_terminal(nullptr)
    , m_commandInputFrame(nullptr)
    , m_statusLabel(nullptr)
    , m_commandInput(nullptr)
    , m_sendBtn(nullptr)
    , m_connectionTypeLabel(nullptr)
    , m_connectionBtn(nullptr)
    , m_commandsTitle(nullptr)
    , m_toggleCommandsBtn(nullptr)
    , m_commandsList(nullptr)
    , m_addCommandBtn(nullptr)
    , m_statusInfo(nullptr)
    , m_versionLabel(nullptr)
    , m_connectionDialog(nullptr)
    , m_logConfigDialog(nullptr)
    , m_backgroundConfigDialog(nullptr)
    , m_commandsPanelVisible(true)
    , m_isConnected(false)
    , m_connectionType("未连接")
    , m_autoSaveTimer(nullptr)
    , m_statusUpdateTimer(nullptr)
    , m_settings(nullptr)
{
    // 初始化核心组件
    m_configManager = new ConfigManager(this);
    m_logManager = new LogManager(this);
    m_commandHistory = new CommandHistory(this);
    m_connectionManager = new ConnectionManager(this);
    
    // 初始化设置
    m_settings = new QSettings(this);
    
    // 设置窗口属性
    setWindowTitle("RF调试工具 v2.0 - Qt版本");
    setMinimumSize(1200, 800);
    
    // 加载窗口配置
    loadWindowConfig();
    
    // 设置UI
    setupUI();
    setupConnections();
    
    // 应用配置
    applyConfig();
    applyMacStyle();
    
    // 加载常用命令
    loadCommonCommands();
    
    // 设置定时器
    m_autoSaveTimer = new QTimer(this);
    m_autoSaveTimer->setInterval(30000); // 30秒自动保存
    connect(m_autoSaveTimer, &QTimer::timeout, this, &MainWindow::autoSave);
    m_autoSaveTimer->start();
    
    m_statusUpdateTimer = new QTimer(this);
    m_statusUpdateTimer->setInterval(1000); // 1秒更新状态
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &MainWindow::updateConnectionStatus);
    m_statusUpdateTimer->start();
    
    // 设置初始状态
    updateConnectionStatus();
    updateCommandButtons();
    
    // 显示欢迎信息
    m_terminal->appendMessage("=== RF调试工具 Qt版本 ===", LogManager::Info);
    m_terminal->appendMessage("", LogManager::Info);
    m_terminal->appendMessage("✅ 功能特性：", LogManager::Info);
    m_terminal->appendMessage("• 多种连接方式：ADB、串口、SSH、网络、FTP", LogManager::Info);
    m_terminal->appendMessage("• 串口高波特率支持：9600-3,000,000", LogManager::Info);
    m_terminal->appendMessage("• 智能日志管理：时间戳、存储、回显配置", LogManager::Info);
    m_terminal->appendMessage("• 背景配置：颜色、图片、透明度（精度0.01）", LogManager::Info);
    m_terminal->appendMessage("• 命令管理：右键菜单、启用/禁用、面板开关", LogManager::Info);
    m_terminal->appendMessage("• Mac风格界面：毛玻璃效果、现代化设计", LogManager::Info);
    m_terminal->appendMessage("", LogManager::Info);
    m_terminal->appendMessage("🚀 开始使用：点击'连接设备'按钮配置连接", LogManager::Info);
    m_terminal->appendMessage("", LogManager::Info);
}

MainWindow::~MainWindow()
{
    // 保存配置
    saveWindowConfig();
    saveCommonCommands();
    
    // 断开连接
    if (m_isConnected && m_connectionManager) {
        m_connectionManager->disconnect();
    }
}

void MainWindow::setupUI()
{
    // 创建中央部件
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);
    
    // 创建各个区域
    createTopMenuArea();
    createMainContentArea();
    createBottomStatusArea();
    
    // 添加到主布局
    mainLayout->addWidget(m_topMenuFrame);
    mainLayout->addWidget(m_mainSplitter, 1);
    
    // 设置状态栏
    setupStatusBar();
}

void MainWindow::createTopMenuArea()
{
    m_topMenuFrame = new QFrame;
    m_topMenuFrame->setObjectName("menuFrame");
    m_topMenuFrame->setFixedHeight(60);
    
    QHBoxLayout *menuLayout = new QHBoxLayout(m_topMenuFrame);
    menuLayout->setContentsMargins(20, 10, 20, 10);
    
    // 菜单标题
    m_menuTitle = new QLabel("RF调试工具");
    m_menuTitle->setObjectName("menuTitle");
    m_menuTitle->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    // 菜单按钮
    m_configBtn = new QPushButton("配置");
    m_configBtn->setObjectName("menuButton");
    
    m_importExportBtn = new QPushButton("导入/导出");
    m_importExportBtn->setObjectName("menuButton");
    
    m_helpBtn = new QPushButton("帮助");
    m_helpBtn->setObjectName("menuButton");
    
    menuLayout->addWidget(m_menuTitle);
    menuLayout->addStretch();
    menuLayout->addWidget(m_configBtn);
    menuLayout->addWidget(m_importExportBtn);
    menuLayout->addWidget(m_helpBtn);
}

void MainWindow::createMainContentArea()
{
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    
    // 创建历史连接面板
    createHistoryPanel();
    
    // 创建主工作区域
    createMainWorkArea();
    
    // 创建常用命令面板
    createCommandsPanel();
    
    // 添加到分割器
    m_mainSplitter->addWidget(m_historyFrame);
    m_mainSplitter->addWidget(m_mainWorkFrame);
    m_mainSplitter->addWidget(m_commandsFrame);
    
    // 设置分割器比例
    m_mainSplitter->setStretchFactor(0, 0); // 历史面板固定宽度
    m_mainSplitter->setStretchFactor(1, 1); // 主工作区域可伸缩
    m_mainSplitter->setStretchFactor(2, 0); // 命令面板固定宽度
    
    // 设置初始大小
    m_mainSplitter->setSizes({80, 800, 150});
}

void MainWindow::createHistoryPanel()
{
    m_historyFrame = new QFrame;
    m_historyFrame->setObjectName("historyFrame");
    m_historyFrame->setFixedWidth(80);
    
    QVBoxLayout *historyLayout = new QVBoxLayout(m_historyFrame);
    historyLayout->setContentsMargins(10, 20, 10, 20);
    historyLayout->setSpacing(10);
    
    QLabel *historyTitle = new QLabel("历\n史\n连\n接\n，\n可\n手\n动\n隐\n藏");
    historyTitle->setObjectName("historyTitle");
    historyTitle->setAlignment(Qt::AlignCenter);
    historyTitle->setWordWrap(true);
    
    historyLayout->addWidget(historyTitle);
    historyLayout->addStretch();
}

void MainWindow::createMainWorkArea()
{
    m_mainWorkFrame = new QFrame;
    m_mainWorkFrame->setObjectName("mainWorkArea");
    
    QVBoxLayout *mainAreaLayout = new QVBoxLayout(m_mainWorkFrame);
    mainAreaLayout->setContentsMargins(20, 20, 20, 20);
    mainAreaLayout->setSpacing(15);
    
    // 创建各个子区域
    createQuickCommandsArea();
    createLogDisplayArea();
    createCommandInputArea();
    
    // 添加到布局
    mainAreaLayout->addWidget(m_quickCommandFrame);
    mainAreaLayout->addWidget(m_logFrame, 1);
    mainAreaLayout->addWidget(m_commandInputFrame);
}

void MainWindow::createQuickCommandsArea()
{
    m_quickCommandFrame = new QFrame;
    m_quickCommandFrame->setObjectName("quickCommandFrame");
    m_quickCommandFrame->setFixedHeight(80);
    
    QHBoxLayout *quickCmdLayout = new QHBoxLayout(m_quickCommandFrame);
    quickCmdLayout->setContentsMargins(20, 15, 20, 15);
    quickCmdLayout->setSpacing(15);
    
    // 快捷命令按钮
    m_cmdBtn1 = new QPushButton("命令1");
    m_cmdBtn1->setObjectName("quickCommandBtn");
    m_cmdBtn1->setFixedSize(80, 40);
    
    m_cmdBtn2 = new QPushButton("命令2");
    m_cmdBtn2->setObjectName("quickCommandBtn");
    m_cmdBtn2->setFixedSize(80, 40);
    
    m_cmdBtn3 = new QPushButton("命令3");
    m_cmdBtn3->setObjectName("quickCommandBtn");
    m_cmdBtn3->setFixedSize(80, 40);
    
    // 快捷命令输入框
    m_quickCmdInput = new QLineEdit;
    m_quickCmdInput->setPlaceholderText("快捷命令框");
    m_quickCmdInput->setObjectName("quickCommandInput");
    m_quickCmdInput->setFixedHeight(40);
    
    quickCmdLayout->addWidget(m_cmdBtn1);
    quickCmdLayout->addWidget(m_cmdBtn2);
    quickCmdLayout->addWidget(m_cmdBtn3);
    quickCmdLayout->addWidget(m_quickCmdInput);
}

void MainWindow::createLogDisplayArea()
{
    m_logFrame = new QFrame;
    m_logFrame->setObjectName("logFrame");
    
    QVBoxLayout *logLayout = new QVBoxLayout(m_logFrame);
    logLayout->setContentsMargins(20, 20, 20, 20);
    
    // 创建交互式终端
    m_terminal = new InteractiveTerminal;
    m_terminal->setObjectName("logDisplay");
    m_terminal->setPlaceholderText("日志显示区域 - 支持命令输入和历史记录");
    
    logLayout->addWidget(m_terminal);
}

void MainWindow::createCommandInputArea()
{
    m_commandInputFrame = new QFrame;
    m_commandInputFrame->setObjectName("commandInputFrame");
    m_commandInputFrame->setFixedHeight(60);
    
    QHBoxLayout *inputLayout = new QHBoxLayout(m_commandInputFrame);
    inputLayout->setContentsMargins(20, 10, 20, 10);
    inputLayout->setSpacing(15);
    
    // 状态标签
    m_statusLabel = new QLabel("状态栏");
    m_statusLabel->setObjectName("statusLabel");
    
    // 命令输入框
    m_commandInput = new QLineEdit;
    m_commandInput->setPlaceholderText("用户命令发送框");
    m_commandInput->setObjectName("commandInput");
    m_commandInput->setFixedHeight(35);
    
    // 发送按钮
    m_sendBtn = new QPushButton("发送");
    m_sendBtn->setObjectName("sendButton");
    m_sendBtn->setFixedSize(60, 35);
    
    // 连接状态标签
    m_connectionTypeLabel = new QLabel("未连接");
    m_connectionTypeLabel->setObjectName("connectionTypeLabel");
    m_connectionTypeLabel->setFixedHeight(35);
    
    // 连接按钮
    m_connectionBtn = new QPushButton("连接设备");
    m_connectionBtn->setObjectName("connectionButton");
    m_connectionBtn->setFixedSize(100, 35);
    
    inputLayout->addWidget(m_statusLabel);
    inputLayout->addWidget(m_commandInput);
    inputLayout->addWidget(m_sendBtn);
    inputLayout->addWidget(m_connectionTypeLabel);
    inputLayout->addWidget(m_connectionBtn);
}

void MainWindow::createCommandsPanel()
{
    m_commandsFrame = new QFrame;
    m_commandsFrame->setObjectName("commandsFrame");
    m_commandsFrame->setFixedWidth(150);
    
    QVBoxLayout *commandsLayout = new QVBoxLayout(m_commandsFrame);
    commandsLayout->setContentsMargins(15, 20, 15, 20);
    commandsLayout->setSpacing(15);
    
    // 标题和控制按钮
    QHBoxLayout *titleLayout = new QHBoxLayout;
    m_commandsTitle = new QLabel("常用命令");
    m_commandsTitle->setObjectName("commandsTitle");
    m_commandsTitle->setAlignment(Qt::AlignCenter);
    
    m_toggleCommandsBtn = new QPushButton("−");
    m_toggleCommandsBtn->setObjectName("toggleButton");
    m_toggleCommandsBtn->setFixedSize(20, 20);
    
    titleLayout->addWidget(m_commandsTitle);
    titleLayout->addWidget(m_toggleCommandsBtn);
    
    // 命令列表
    m_commandsList = new CommandListWidget;
    m_commandsList->setObjectName("commandsList");
    
    // 添加命令按钮
    m_addCommandBtn = new QPushButton("+ 添加命令");
    m_addCommandBtn->setObjectName("addCommandBtn");
    
    commandsLayout->addLayout(titleLayout);
    commandsLayout->addWidget(m_commandsList);
    commandsLayout->addWidget(m_addCommandBtn);
}

void MainWindow::createBottomStatusArea()
{
    // 状态栏在setupStatusBar中创建
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    
    // 状态信息
    m_statusInfo = new QLabel("就绪");
    m_statusInfo->setObjectName("statusInfo");
    
    // 版本信息
    m_versionLabel = new QLabel("RF调试工具 v2.0 - Qt版本");
    m_versionLabel->setObjectName("versionLabel");
    
    m_statusBar->addWidget(m_statusInfo);
    m_statusBar->addPermanentWidget(m_versionLabel);
}

void MainWindow::setupConnections()
{
    // 菜单按钮连接
    connect(m_configBtn, &QPushButton::clicked, this, &MainWindow::showConfigMenu);
    connect(m_importExportBtn, &QPushButton::clicked, this, &MainWindow::showImportExportMenu);
    connect(m_helpBtn, &QPushButton::clicked, this, &MainWindow::showHelp);
    
    // 快捷命令按钮连接
    connect(m_cmdBtn1, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    connect(m_cmdBtn2, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    connect(m_cmdBtn3, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    
    // 命令输入连接
    connect(m_commandInput, &QLineEdit::returnPressed, this, &MainWindow::sendCommand);
    connect(m_quickCmdInput, &QLineEdit::returnPressed, this, &MainWindow::sendCommand);
    connect(m_sendBtn, &QPushButton::clicked, this, &MainWindow::sendCommand);
    
    // 连接按钮
    connect(m_connectionBtn, &QPushButton::clicked, this, &MainWindow::showConnectionDialog);
    
    // 命令面板
    connect(m_toggleCommandsBtn, &QPushButton::clicked, this, &MainWindow::toggleCommandsPanel);
    connect(m_addCommandBtn, &QPushButton::clicked, this, &MainWindow::addNewCommand);
    
    // 连接管理器信号
    if (m_connectionManager) {
        connect(m_connectionManager, &ConnectionManager::statusChanged,
                this, &MainWindow::onConnectionStatusChanged);
        connect(m_connectionManager, &ConnectionManager::dataReceived,
                this, &MainWindow::onDataReceived);
        connect(m_connectionManager, &ConnectionManager::errorOccurred,
                this, &MainWindow::onErrorOccurred);
    }
    
    // 配置管理器信号
    if (m_configManager) {
        connect(m_configManager, &ConfigManager::configChanged,
                this, &MainWindow::onConfigChanged);
    }
    
    // 命令列表信号
    if (m_commandsList) {
        connect(m_commandsList, &CommandListWidget::executeCommand,
                this, &MainWindow::executeCommonCommand);
        connect(m_commandsList, &CommandListWidget::commandEdited,
                this, &MainWindow::onCommandEdited);
        connect(m_commandsList, &CommandListWidget::commandDeleted,
                this, &MainWindow::onCommandDeleted);
        connect(m_commandsList, &CommandListWidget::commandAdded,
                this, &MainWindow::onCommandAdded);
    }
    
    // 终端信号
    if (m_terminal) {
        connect(m_terminal, &InteractiveTerminal::commandEntered,
                this, [this](const QString &command) {
                    if (m_connectionManager && m_isConnected) {
                        m_connectionManager->sendCommand(command);
                        m_commandHistory->addCommand(command);
                    } else {
                        m_terminal->appendMessage("警告: 设备未连接", LogManager::Warning);
                    }
                });
    }
}
