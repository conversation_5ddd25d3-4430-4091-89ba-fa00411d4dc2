QT += core widgets serialport network

CONFIG += c++17

TARGET = RFTool_Qt_Test
TEMPLATE = app

# 最小化测试 - 只包含核心文件
SOURCES += \
    main.cpp \
    MainWindow.cpp \
    ConnectionManager.cpp \
    ConfigManager.cpp \
    LogManager.cpp \
    InteractiveTerminal.cpp \
    CommandHistory.cpp \
    SerialConnection.cpp

HEADERS += \
    MainWindow.h \
    ConnectionManager.h \
    ConfigManager.h \
    LogManager.h \
    InteractiveTerminal.h \
    CommandHistory.h \
    SerialConnection.h

# 编译器设置
win32 {
    CONFIG += console
    DEFINES += WIN32_LEAN_AND_MEAN
}

# 输出目录
DESTDIR = $$PWD/test_bin
OBJECTS_DIR = $$PWD/test_build/obj
MOC_DIR = $$PWD/test_build/moc
RCC_DIR = $$PWD/test_build/rcc
UI_DIR = $$PWD/test_build/ui
