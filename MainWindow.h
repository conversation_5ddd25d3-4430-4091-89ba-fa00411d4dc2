#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QFrame>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QListWidget>
#include <QTabWidget>
#include <QToolBar>
#include <QStatusBar>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QSettings>
#include <QCloseEvent>
#include <QResizeEvent>
#include <QShowEvent>

// 前向声明
class ConnectionManager;
class ConfigManager;
class LogManager;
class CommandHistory;
class InteractiveTerminal;
class CommandListWidget;
class ConnectionDialog;
class LogConfigDialog;
class BackgroundConfigDialog;

QT_BEGIN_NAMESPACE
class QAction;
class QMenu;
class QToolBar;
class QStatusBar;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;

private slots:
    // 连接相关
    void showConnectionDialog();
    void connectDevice();
    void disconnectDevice();
    void onConnectionStatusChanged(const QString &status, const QString &details);
    void onDataReceived(const QString &data);
    void onErrorOccurred(const QString &error);
    
    // 命令相关
    void sendCommand();
    void executeQuickCommand();
    void executeCommonCommand(const QString &name, const QString &content);
    void onCommandEdited(const QString &oldName, const QString &newName, const QString &newContent);
    void onCommandDeleted(const QString &name);
    void onCommandAdded(const QString &name, const QString &content);
    void addNewCommand();
    
    // 配置相关
    void showConfigMenu();
    void showLogConfig();
    void showBackgroundConfig();
    void showUIConfig();
    void showTerminalConfig();
    void resetConfig();
    void onConfigChanged(const QString &key, const QVariant &value);
    
    // 导入导出
    void showImportExportMenu();
    void exportConfig();
    void importConfig();
    void exportCommands();
    void importCommands();
    
    // 界面相关
    void toggleCommandsPanel();
    void updateConnectionStatus();
    void updateCommandButtons();
    void applyBackgroundConfig();
    
    // 其他
    void showHelp();
    void autoSave();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupCentralWidget();
    void setupStatusBar();
    void setupConnections();
    
    void createTopMenuArea();
    void createMainContentArea();
    void createHistoryPanel();
    void createMainWorkArea();
    void createCommandsPanel();
    void createQuickCommandsArea();
    void createLogDisplayArea();
    void createCommandInputArea();
    void createBottomStatusArea();
    
    void applyMacStyle();
    void loadWindowConfig();
    void saveWindowConfig();
    void loadCommonCommands();
    void saveCommonCommands();
    void applyConfig();
    
    QString buildBackgroundStyle(const QVariantMap &config);
    void updateMainWindowStyle(const QString &bgStyle);

private:
    // 核心组件
    ConnectionManager *m_connectionManager;
    ConfigManager *m_configManager;
    LogManager *m_logManager;
    CommandHistory *m_commandHistory;
    
    // UI组件
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QFrame *m_historyFrame;
    QFrame *m_mainWorkFrame;
    QFrame *m_commandsFrame;
    
    // 菜单和工具栏
    QMenuBar *m_menuBar;
    QToolBar *m_toolBar;
    QStatusBar *m_statusBar;
    
    // 顶部菜单区域
    QFrame *m_topMenuFrame;
    QLabel *m_menuTitle;
    QPushButton *m_configBtn;
    QPushButton *m_importExportBtn;
    QPushButton *m_helpBtn;
    
    // 快捷命令区域
    QFrame *m_quickCommandFrame;
    QPushButton *m_cmdBtn1;
    QPushButton *m_cmdBtn2;
    QPushButton *m_cmdBtn3;
    QLineEdit *m_quickCmdInput;
    
    // 日志显示区域
    QFrame *m_logFrame;
    InteractiveTerminal *m_terminal;
    
    // 命令输入区域
    QFrame *m_commandInputFrame;
    QLabel *m_statusLabel;
    QLineEdit *m_commandInput;
    QPushButton *m_sendBtn;
    QLabel *m_connectionTypeLabel;
    QPushButton *m_connectionBtn;
    
    // 常用命令面板
    QLabel *m_commandsTitle;
    QPushButton *m_toggleCommandsBtn;
    CommandListWidget *m_commandsList;
    QPushButton *m_addCommandBtn;
    
    // 状态信息
    QLabel *m_statusInfo;
    QLabel *m_versionLabel;
    
    // 对话框
    ConnectionDialog *m_connectionDialog;
    LogConfigDialog *m_logConfigDialog;
    BackgroundConfigDialog *m_backgroundConfigDialog;
    
    // 状态变量
    bool m_commandsPanelVisible;
    bool m_isConnected;
    QString m_connectionType;
    QVariantMap m_connectionParams;
    
    // 定时器
    QTimer *m_autoSaveTimer;
    QTimer *m_statusUpdateTimer;
    
    // 设置
    QSettings *m_settings;
};

#endif // MAINWINDOW_H
