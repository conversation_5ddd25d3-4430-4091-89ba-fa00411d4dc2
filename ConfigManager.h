#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>
#include <QVariantMap>
#include <QStringList>
#include <QTimer>
#include <QFileSystemWatcher>
#include <QMutex>
#include <QJsonObject>
#include <QJsonDocument>

class ConfigManager : public QObject
{
    Q_OBJECT

public:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // 基本配置操作
    void setValue(const QString &key, const QVariant &value);
    QVariant getValue(const QString &key, const QVariant &defaultValue = QVariant()) const;
    bool contains(const QString &key) const;
    void removeKey(const QString &key);
    QStringList allKeys() const;

    // 分组配置操作
    void beginGroup(const QString &prefix);
    void endGroup();
    QString group() const;
    QStringList childGroups() const;
    QStringList childKeys() const;

    // 批量操作
    void setValues(const QVariantMap &values);
    QVariantMap getValues(const QStringList &keys) const;
    QVariantMap getAllValues() const;

    // 配置文件管理
    void setConfigFile(const QString &filePath);
    QString configFile() const;
    bool loadFromFile(const QString &filePath = QString());
    bool saveToFile(const QString &filePath = QString());
    void reloadConfig();

    // 自动保存
    void setAutoSaveEnabled(bool enabled);
    bool isAutoSaveEnabled() const;
    void setAutoSaveInterval(int seconds);
    int autoSaveInterval() const;

    // 备份管理
    void setBackupEnabled(bool enabled);
    bool isBackupEnabled() const;
    void setMaxBackupFiles(int count);
    int maxBackupFiles() const;
    bool createBackup();
    QStringList getBackupFiles() const;
    bool restoreFromBackup(const QString &backupFile);

    // 配置验证
    void setValidationEnabled(bool enabled);
    bool isValidationEnabled() const;
    void addValidationRule(const QString &key, const QVariant &minValue, const QVariant &maxValue);
    void addValidationRule(const QString &key, const QStringList &allowedValues);
    bool validateConfig() const;
    QStringList getValidationErrors() const;

    // 默认配置
    void setDefaultValues(const QVariantMap &defaults);
    QVariantMap defaultValues() const;
    void resetToDefaults();
    void resetKey(const QString &key);

    // 导入导出
    bool exportConfig(const QString &filePath, const QStringList &keys = QStringList()) const;
    bool importConfig(const QString &filePath, bool merge = true);
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject &json);

    // 命令配置
    void setCommands(const QVariantList &commands);
    QVariantList getCommands() const;
    void addCommand(const QVariantMap &command);
    void removeCommand(const QString &name);
    void updateCommand(const QString &name, const QVariantMap &command);
    QVariantMap getCommand(const QString &name) const;

    // 连接历史
    void addConnectionHistory(const QVariantMap &connection);
    QVariantList getConnectionHistory() const;
    void clearConnectionHistory();
    void setMaxHistorySize(int size);

    // 窗口状态
    void saveWindowState(const QString &windowName, const QByteArray &state);
    QByteArray getWindowState(const QString &windowName) const;
    void saveWindowGeometry(const QString &windowName, const QByteArray &geometry);
    QByteArray getWindowGeometry(const QString &windowName) const;

public slots:
    void save();
    void load();
    void sync();

signals:
    void configChanged(const QString &key, const QVariant &value);
    void configLoaded();
    void configSaved();
    void configError(const QString &error);
    void validationFailed(const QStringList &errors);

private slots:
    void onAutoSaveTimer();
    void onConfigFileChanged(const QString &path);

private:
    void initializeDefaults();
    void setupAutoSave();
    void setupFileWatcher();
    QString getBackupFileName() const;
    void cleanupOldBackups();
    bool validateValue(const QString &key, const QVariant &value) const;
    QVariant sanitizeValue(const QString &key, const QVariant &value) const;
    void notifyConfigChange(const QString &key, const QVariant &value);

private:
    // 核心设置对象
    QSettings *m_settings;
    QString m_configFilePath;
    
    // 配置数据
    QVariantMap m_configData;
    QVariantMap m_defaultValues;
    
    // 自动保存
    bool m_autoSaveEnabled;
    int m_autoSaveInterval;
    QTimer *m_autoSaveTimer;
    bool m_hasUnsavedChanges;
    
    // 备份管理
    bool m_backupEnabled;
    int m_maxBackupFiles;
    QString m_backupDirectory;
    
    // 配置验证
    bool m_validationEnabled;
    struct ValidationRule {
        QVariant minValue;
        QVariant maxValue;
        QStringList allowedValues;
        bool hasRange;
        bool hasAllowedValues;
    };
    QMap<QString, ValidationRule> m_validationRules;
    
    // 文件监控
    QFileSystemWatcher *m_fileWatcher;
    bool m_ignoreFileChanges;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 分组状态
    QStringList m_groupStack;
    
    // 历史管理
    int m_maxHistorySize;
    
    // 错误信息
    mutable QStringList m_lastValidationErrors;
};

#endif // CONFIGMANAGER_H
