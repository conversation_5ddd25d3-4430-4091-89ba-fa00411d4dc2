#include "CommandHistory.h"
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QMutexLocker>
#include <QRegularExpression>

CommandHistory::CommandHistory(QObject *parent)
    : QObject(parent), m_navigationIndex(-1), m_isNavigating(false), m_maxHistorySize(1000), m_autoSaveEnabled(true), m_autoSaveInterval(30)
{
    // 设置默认历史文件路径
    m_historyFilePath = getDefaultHistoryFile();

    // 初始化自动保存定时器
    m_autoSaveTimer = new QTimer(this);
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
    connect(m_autoSaveTimer, &QTimer::timeout, this, &CommandHistory::onAutoSaveTimer);

    if (m_autoSaveEnabled)
    {
        m_autoSaveTimer->start();
    }

    // 加载历史记录
    loadFromFile();
}

CommandHistory::~CommandHistory()
{
    if (m_autoSaveEnabled)
    {
        saveToFile();
    }
}

void CommandHistory::addCommand(const QString &command, const QString &result, bool success, int executionTime)
{
    QMutexLocker locker(&m_mutex);

    if (command.trimmed().isEmpty())
    {
        return;
    }

    HistoryItem item;
    item.command = command.trimmed();
    item.timestamp = QDateTime::currentDateTime();
    item.result = result;
    item.success = success;
    item.executionTime = executionTime;

    // 添加到历史记录
    m_history.append(item);
    m_commands.append(item.command);

    // 更新使用统计
    m_commandUsageCount[item.command]++;

    // 限制历史记录大小
    trimHistory();

    // 重置导航索引
    resetNavigation();

    emit commandAdded(command);
}

void CommandHistory::clearHistory()
{
    QMutexLocker locker(&m_mutex);

    m_history.clear();
    m_commands.clear();
    m_commandUsageCount.clear();
    resetNavigation();

    emit historyCleared();
}

QStringList CommandHistory::getCommands() const
{
    QMutexLocker locker(&m_mutex);
    return m_commands;
}

QList<CommandHistory::HistoryItem> CommandHistory::getHistory() const
{
    QMutexLocker locker(&m_mutex);
    return m_history;
}

CommandHistory::HistoryItem CommandHistory::getHistoryItem(int index) const
{
    QMutexLocker locker(&m_mutex);

    if (index >= 0 && index < m_history.size())
    {
        return m_history.at(index);
    }

    return HistoryItem();
}

int CommandHistory::historySize() const
{
    QMutexLocker locker(&m_mutex);
    return m_history.size();
}

QString CommandHistory::getPreviousCommand()
{
    QMutexLocker locker(&m_mutex);

    if (m_commands.isEmpty())
    {
        return QString();
    }

    if (!m_isNavigating)
    {
        m_navigationIndex = m_commands.size();
        m_isNavigating = true;
    }

    if (m_navigationIndex > 0)
    {
        m_navigationIndex--;
        return m_commands.at(m_navigationIndex);
    }

    return QString();
}

QString CommandHistory::getNextCommand()
{
    QMutexLocker locker(&m_mutex);

    if (m_commands.isEmpty() || !m_isNavigating)
    {
        return QString();
    }

    if (m_navigationIndex < m_commands.size() - 1)
    {
        m_navigationIndex++;
        return m_commands.at(m_navigationIndex);
    }
    else
    {
        // 到达最新命令后，返回空字符串
        m_navigationIndex = m_commands.size();
        return QString();
    }
}

void CommandHistory::resetNavigation()
{
    m_navigationIndex = -1;
    m_isNavigating = false;
}

int CommandHistory::currentIndex() const
{
    return m_navigationIndex;
}

QStringList CommandHistory::searchCommands(const QString &pattern) const
{
    QMutexLocker locker(&m_mutex);

    QStringList results;
    QRegularExpression regex(pattern, QRegularExpression::CaseInsensitiveOption);

    for (const QString &command : m_commands)
    {
        if (regex.match(command).hasMatch())
        {
            results.append(command);
        }
    }

    return results;
}

QList<CommandHistory::HistoryItem> CommandHistory::searchHistory(const QString &pattern) const
{
    QMutexLocker locker(&m_mutex);

    QList<HistoryItem> results;
    QRegularExpression regex(pattern, QRegularExpression::CaseInsensitiveOption);

    for (const HistoryItem &item : m_history)
    {
        if (regex.match(item.command).hasMatch())
        {
            results.append(item);
        }
    }

    return results;
}

void CommandHistory::setMaxHistorySize(int size)
{
    QMutexLocker locker(&m_mutex);
    m_maxHistorySize = qMax(1, size);
    trimHistory();
}

int CommandHistory::maxHistorySize() const
{
    return m_maxHistorySize;
}

void CommandHistory::setAutoSave(bool enabled)
{
    m_autoSaveEnabled = enabled;

    if (enabled)
    {
        m_autoSaveTimer->start();
    }
    else
    {
        m_autoSaveTimer->stop();
    }
}

bool CommandHistory::isAutoSaveEnabled() const
{
    return m_autoSaveEnabled;
}

void CommandHistory::setAutoSaveInterval(int seconds)
{
    m_autoSaveInterval = qMax(1, seconds);
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
}

int CommandHistory::autoSaveInterval() const
{
    return m_autoSaveInterval;
}

void CommandHistory::trimHistory()
{
    while (m_history.size() > m_maxHistorySize)
    {
        HistoryItem removed = m_history.takeFirst();
        m_commands.removeFirst();

        // 更新使用统计
        m_commandUsageCount[removed.command]--;
        if (m_commandUsageCount[removed.command] <= 0)
        {
            m_commandUsageCount.remove(removed.command);
        }
    }
}

QString CommandHistory::getDefaultHistoryFile() const
{
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(dataDir);
    return QDir(dataDir).filePath("command_history.json");
}

void CommandHistory::onAutoSaveTimer()
{
    saveToFile();
}

bool CommandHistory::saveToFile(const QString &filePath) const
{
    QMutexLocker locker(&m_mutex);

    QString path = filePath.isEmpty() ? m_historyFilePath : filePath;

    QJsonArray historyArray;
    for (const HistoryItem &item : m_history)
    {
        QJsonObject itemObj;
        itemObj["command"] = item.command;
        itemObj["timestamp"] = item.timestamp.toString(Qt::ISODate);
        itemObj["result"] = item.result;
        itemObj["success"] = item.success;
        itemObj["executionTime"] = item.executionTime;
        historyArray.append(itemObj);
    }

    QJsonObject rootObj;
    rootObj["version"] = "1.0";
    rootObj["maxHistorySize"] = m_maxHistorySize;
    rootObj["history"] = historyArray;

    QJsonDocument doc(rootObj);

    QFile file(path);
    if (!file.open(QIODevice::WriteOnly))
    {
        return false;
    }

    file.write(doc.toJson());
    file.close();

    emit const_cast<CommandHistory *>(this)->historySaved();
    return true;
}

bool CommandHistory::loadFromFile(const QString &filePath)
{
    QMutexLocker locker(&m_mutex);

    QString path = filePath.isEmpty() ? m_historyFilePath : filePath;

    QFile file(path);
    if (!file.open(QIODevice::ReadOnly))
    {
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError)
    {
        return false;
    }

    QJsonObject rootObj = doc.object();

    // 清空当前历史
    m_history.clear();
    m_commands.clear();
    m_commandUsageCount.clear();

    // 加载历史记录
    QJsonArray historyArray = rootObj["history"].toArray();
    for (const QJsonValue &value : historyArray)
    {
        QJsonObject itemObj = value.toObject();

        HistoryItem item;
        item.command = itemObj["command"].toString();
        item.timestamp = QDateTime::fromString(itemObj["timestamp"].toString(), Qt::ISODate);
        item.result = itemObj["result"].toString();
        item.success = itemObj["success"].toBool();
        item.executionTime = itemObj["executionTime"].toInt();

        m_history.append(item);
        m_commands.append(item.command);
        m_commandUsageCount[item.command]++;
    }

    // 更新配置
    if (rootObj.contains("maxHistorySize"))
    {
        m_maxHistorySize = rootObj["maxHistorySize"].toInt();
    }

    resetNavigation();
    emit historyLoaded();
    return true;
}

void CommandHistory::setHistoryFile(const QString &filePath)
{
    m_historyFilePath = filePath;
}

QString CommandHistory::historyFile() const
{
    return m_historyFilePath;
}

int CommandHistory::getTotalCommands() const
{
    QMutexLocker locker(&m_mutex);
    return m_history.size();
}

QDateTime CommandHistory::getFirstCommandTime() const
{
    QMutexLocker locker(&m_mutex);

    if (m_history.isEmpty())
    {
        return QDateTime();
    }

    return m_history.first().timestamp;
}

QDateTime CommandHistory::getLastCommandTime() const
{
    QMutexLocker locker(&m_mutex);

    if (m_history.isEmpty())
    {
        return QDateTime();
    }

    return m_history.last().timestamp;
}

QStringList CommandHistory::getMostUsedCommands(int count) const
{
    QMutexLocker locker(&m_mutex);

    // 创建使用次数和命令的配对列表
    QList<QPair<int, QString>> usagePairs;
    for (auto it = m_commandUsageCount.begin(); it != m_commandUsageCount.end(); ++it)
    {
        usagePairs.append(qMakePair(it.value(), it.key()));
    }

    // 按使用次数降序排序
    std::sort(usagePairs.begin(), usagePairs.end(), [](const QPair<int, QString> &a, const QPair<int, QString> &b)
              { return a.first > b.first; });

    // 提取前count个命令
    QStringList result;
    for (int i = 0; i < qMin(count, usagePairs.size()); ++i)
    {
        result.append(usagePairs.at(i).second);
    }

    return result;
}

void CommandHistory::save()
{
    saveToFile();
}

void CommandHistory::load()
{
    loadFromFile();
}
