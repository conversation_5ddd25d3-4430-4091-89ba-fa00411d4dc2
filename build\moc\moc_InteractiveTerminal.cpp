/****************************************************************************
** Meta object code from reading C++ file 'InteractiveTerminal.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../InteractiveTerminal.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'InteractiveTerminal.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_InteractiveTerminal_t {
    QByteArrayData data[17];
    char stringdata0[209];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_InteractiveTerminal_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_InteractiveTerminal_t qt_meta_stringdata_InteractiveTerminal = {
    {
QT_MOC_LITERAL(0, 0, 19), // "InteractiveTerminal"
QT_MOC_LITERAL(1, 20, 14), // "commandEntered"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 7), // "command"
QT_MOC_LITERAL(4, 44, 10), // "logMessage"
QT_MOC_LITERAL(5, 55, 7), // "message"
QT_MOC_LITERAL(6, 63, 20), // "LogManager::LogLevel"
QT_MOC_LITERAL(7, 84, 5), // "level"
QT_MOC_LITERAL(8, 90, 15), // "messageAppended"
QT_MOC_LITERAL(9, 106, 15), // "terminalCleared"
QT_MOC_LITERAL(10, 122, 4), // "copy"
QT_MOC_LITERAL(11, 127, 9), // "selectAll"
QT_MOC_LITERAL(12, 137, 23), // "onCursorPositionChanged"
QT_MOC_LITERAL(13, 161, 13), // "onTextChanged"
QT_MOC_LITERAL(14, 175, 13), // "saveToLogFile"
QT_MOC_LITERAL(15, 189, 15), // "showContextMenu"
QT_MOC_LITERAL(16, 205, 3) // "pos"

    },
    "InteractiveTerminal\0commandEntered\0\0"
    "command\0logMessage\0message\0"
    "LogManager::LogLevel\0level\0messageAppended\0"
    "terminalCleared\0copy\0selectAll\0"
    "onCursorPositionChanged\0onTextChanged\0"
    "saveToLogFile\0showContextMenu\0pos"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_InteractiveTerminal[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   64,    2, 0x06 /* Public */,
       4,    2,   67,    2, 0x06 /* Public */,
       8,    2,   72,    2, 0x06 /* Public */,
       9,    0,   77,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      10,    0,   78,    2, 0x0a /* Public */,
      11,    0,   79,    2, 0x0a /* Public */,
      12,    0,   80,    2, 0x08 /* Private */,
      13,    0,   81,    2, 0x08 /* Private */,
      14,    0,   82,    2, 0x08 /* Private */,
      15,    1,   83,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 6,    5,    7,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 6,    5,    7,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   16,

       0        // eod
};

void InteractiveTerminal::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<InteractiveTerminal *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->commandEntered((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->logMessage((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< LogManager::LogLevel(*)>(_a[2]))); break;
        case 2: _t->messageAppended((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< LogManager::LogLevel(*)>(_a[2]))); break;
        case 3: _t->terminalCleared(); break;
        case 4: _t->copy(); break;
        case 5: _t->selectAll(); break;
        case 6: _t->onCursorPositionChanged(); break;
        case 7: _t->onTextChanged(); break;
        case 8: _t->saveToLogFile(); break;
        case 9: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (InteractiveTerminal::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&InteractiveTerminal::commandEntered)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (InteractiveTerminal::*)(const QString & , LogManager::LogLevel );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&InteractiveTerminal::logMessage)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (InteractiveTerminal::*)(const QString & , LogManager::LogLevel );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&InteractiveTerminal::messageAppended)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (InteractiveTerminal::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&InteractiveTerminal::terminalCleared)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject InteractiveTerminal::staticMetaObject = { {
    &QTextEdit::staticMetaObject,
    qt_meta_stringdata_InteractiveTerminal.data,
    qt_meta_data_InteractiveTerminal,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *InteractiveTerminal::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *InteractiveTerminal::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_InteractiveTerminal.stringdata0))
        return static_cast<void*>(this);
    return QTextEdit::qt_metacast(_clname);
}

int InteractiveTerminal::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QTextEdit::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void InteractiveTerminal::commandEntered(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void InteractiveTerminal::logMessage(const QString & _t1, LogManager::LogLevel _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void InteractiveTerminal::messageAppended(const QString & _t1, LogManager::LogLevel _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void InteractiveTerminal::terminalCleared()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
