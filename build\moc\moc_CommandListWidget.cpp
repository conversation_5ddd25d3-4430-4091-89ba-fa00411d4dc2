/****************************************************************************
** Meta object code from reading C++ file 'CommandListWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../CommandListWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CommandListWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CommandListWidget_t {
    QByteArrayData data[22];
    char stringdata0[267];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CommandListWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CommandListWidget_t qt_meta_stringdata_CommandListWidget = {
    {
QT_MOC_LITERAL(0, 0, 17), // "CommandListWidget"
QT_MOC_LITERAL(1, 18, 14), // "executeCommand"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 4), // "name"
QT_MOC_LITERAL(4, 39, 7), // "content"
QT_MOC_LITERAL(5, 47, 12), // "commandAdded"
QT_MOC_LITERAL(6, 60, 13), // "commandEdited"
QT_MOC_LITERAL(7, 74, 7), // "oldName"
QT_MOC_LITERAL(8, 82, 7), // "newName"
QT_MOC_LITERAL(9, 90, 10), // "newContent"
QT_MOC_LITERAL(10, 101, 14), // "commandDeleted"
QT_MOC_LITERAL(11, 116, 14), // "commandEnabled"
QT_MOC_LITERAL(12, 131, 7), // "enabled"
QT_MOC_LITERAL(13, 139, 16), // "onExecuteCommand"
QT_MOC_LITERAL(14, 156, 13), // "onEditCommand"
QT_MOC_LITERAL(15, 170, 15), // "onDeleteCommand"
QT_MOC_LITERAL(16, 186, 15), // "onToggleCommand"
QT_MOC_LITERAL(17, 202, 13), // "onCopyCommand"
QT_MOC_LITERAL(18, 216, 8), // "onMoveUp"
QT_MOC_LITERAL(19, 225, 10), // "onMoveDown"
QT_MOC_LITERAL(20, 236, 16), // "onShowProperties"
QT_MOC_LITERAL(21, 253, 13) // "onFilterTimer"

    },
    "CommandListWidget\0executeCommand\0\0"
    "name\0content\0commandAdded\0commandEdited\0"
    "oldName\0newName\0newContent\0commandDeleted\0"
    "commandEnabled\0enabled\0onExecuteCommand\0"
    "onEditCommand\0onDeleteCommand\0"
    "onToggleCommand\0onCopyCommand\0onMoveUp\0"
    "onMoveDown\0onShowProperties\0onFilterTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CommandListWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   84,    2, 0x06 /* Public */,
       5,    2,   89,    2, 0x06 /* Public */,
       6,    3,   94,    2, 0x06 /* Public */,
      10,    1,  101,    2, 0x06 /* Public */,
      11,    2,  104,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    0,  109,    2, 0x08 /* Private */,
      14,    0,  110,    2, 0x08 /* Private */,
      15,    0,  111,    2, 0x08 /* Private */,
      16,    0,  112,    2, 0x08 /* Private */,
      17,    0,  113,    2, 0x08 /* Private */,
      18,    0,  114,    2, 0x08 /* Private */,
      19,    0,  115,    2, 0x08 /* Private */,
      20,    0,  116,    2, 0x08 /* Private */,
      21,    0,  117,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QString,    7,    8,    9,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    3,   12,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void CommandListWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CommandListWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->executeCommand((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 1: _t->commandAdded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 2: _t->commandEdited((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 3: _t->commandDeleted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->commandEnabled((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 5: _t->onExecuteCommand(); break;
        case 6: _t->onEditCommand(); break;
        case 7: _t->onDeleteCommand(); break;
        case 8: _t->onToggleCommand(); break;
        case 9: _t->onCopyCommand(); break;
        case 10: _t->onMoveUp(); break;
        case 11: _t->onMoveDown(); break;
        case 12: _t->onShowProperties(); break;
        case 13: _t->onFilterTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CommandListWidget::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandListWidget::executeCommand)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CommandListWidget::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandListWidget::commandAdded)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CommandListWidget::*)(const QString & , const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandListWidget::commandEdited)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (CommandListWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandListWidget::commandDeleted)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (CommandListWidget::*)(const QString & , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandListWidget::commandEnabled)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CommandListWidget::staticMetaObject = { {
    &QListWidget::staticMetaObject,
    qt_meta_stringdata_CommandListWidget.data,
    qt_meta_data_CommandListWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CommandListWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CommandListWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CommandListWidget.stringdata0))
        return static_cast<void*>(this);
    return QListWidget::qt_metacast(_clname);
}

int CommandListWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QListWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 14;
    }
    return _id;
}

// SIGNAL 0
void CommandListWidget::executeCommand(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CommandListWidget::commandAdded(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CommandListWidget::commandEdited(const QString & _t1, const QString & _t2, const QString & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void CommandListWidget::commandDeleted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void CommandListWidget::commandEnabled(const QString & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
