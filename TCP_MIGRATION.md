# SSH到TCP连接迁移说明

## 概述

已成功将SSH连接功能替换为TCP连接功能，提供更简单、更直接的网络连接方式。

## 主要变更

### 1. 新增文件
- **TCPConnection.h** - TCP连接类头文件
- **TCPConnection.cpp** - TCP连接类实现

### 2. 删除文件
- **SSHConnection.h** - 已删除
- **SSHConnection.cpp** - 已删除

### 3. 修改文件

#### ConnectionManager.h/.cpp
- 将 `SSHConnection` 改为 `TCPConnection`
- 更新枚举 `SSH` 为 `TCP`
- 更新所有相关的方法和变量名

#### ConnectionDialog.h/.cpp
- 将SSH标签页改为TCP标签页
- 更新UI控件和参数处理
- 简化配置选项，专注于TCP连接

#### 项目文件
- **RFTool_Qt.pro** - 更新源文件和头文件列表

## TCP连接功能特性

### 🔌 连接配置
- **主机地址**: 支持IP地址和域名
- **端口**: 1-65535端口范围
- **协议**: TCP/UDP选择
- **超时设置**: 1-300秒可配置

### 📡 数据处理
- **行结束符**: 可自定义（如\r\n, \n等）
- **编码格式**: 支持UTF-8, ASCII, GBK, GB2312
- **保活机制**: 可选的连接保活
- **数据缓冲**: 智能数据接收和发送

### 🛠️ 高级功能
- **自动重连**: 连接断开后自动重连
- **连接状态监控**: 实时连接状态反馈
- **错误处理**: 详细的错误信息和恢复机制
- **统计信息**: 字节数、命令数、连接时间统计

## 使用方法

### 1. 在连接对话框中
1. 选择"TCP"标签页
2. 输入目标主机地址
3. 设置端口号（默认8080）
4. 选择协议类型（TCP/UDP）
5. 配置其他选项（可选）
6. 点击"连接"

### 2. 连接参数示例
```json
{
    "host": "*************",
    "port": 8080,
    "protocol": "TCP",
    "line_ending": "\r\n",
    "encoding": "UTF-8",
    "keep_alive": true,
    "timeout": 30
}
```

### 3. 编程接口
```cpp
// 创建TCP连接
TCPConnection *tcp = new TCPConnection(this);

// 设置连接参数
QVariantMap params;
params["host"] = "*************";
params["port"] = 8080;
params["protocol"] = "TCP";

// 连接
tcp->connect(params);

// 发送数据
tcp->sendCommand("Hello World");
tcp->sendData(QByteArray("Binary Data"));
```

## 与SSH的主要区别

| 特性 | SSH | TCP |
|------|-----|-----|
| 安全性 | 加密连接 | 明文传输 |
| 认证 | 用户名/密码/密钥 | 无认证 |
| 复杂度 | 高 | 低 |
| 性能 | 较低（加密开销） | 高 |
| 适用场景 | 远程管理 | 设备调试 |
| 配置难度 | 复杂 | 简单 |

## 优势

### ✅ 简化配置
- 无需SSH服务器配置
- 无需用户认证设置
- 直接的点对点连接

### ✅ 更好的性能
- 无加密解密开销
- 更低的延迟
- 更高的吞吐量

### ✅ 更适合调试
- 原始数据传输
- 更容易的协议分析
- 支持自定义数据格式

### ✅ 更广泛的兼容性
- 支持各种TCP/UDP设备
- 无需特定的SSH支持
- 更简单的设备集成

## 注意事项

### ⚠️ 安全性
- TCP连接是明文传输，不适合敏感数据
- 建议在可信网络环境中使用
- 如需安全传输，考虑使用VPN或其他加密方案

### ⚠️ 网络配置
- 确保目标端口未被防火墙阻止
- 检查网络连通性
- 注意NAT和路由配置

### ⚠️ 设备兼容性
- 确认目标设备支持TCP连接
- 验证数据格式和协议
- 测试连接稳定性

## 编译说明

项目现在使用TCPConnection替代SSHConnection，编译时：

1. 确保Qt Network模块已安装
2. 使用更新后的.pro文件
3. 清理并重新编译项目

```bash
# 清理项目
make clean

# 重新生成Makefile
qmake RFTool_Qt.pro

# 编译
make
```

## 测试建议

1. **基本连接测试**: 连接到已知的TCP服务器
2. **数据传输测试**: 发送和接收各种数据格式
3. **错误处理测试**: 测试网络断开、超时等情况
4. **性能测试**: 测试大数据量传输
5. **稳定性测试**: 长时间连接测试

TCP连接功能现在已完全替代SSH功能，提供更适合RF调试工具的网络连接方案！
