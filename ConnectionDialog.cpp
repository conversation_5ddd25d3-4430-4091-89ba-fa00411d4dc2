#include "ConnectionDialog.h"
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>

ConnectionDialog::ConnectionDialog(QWidget *parent)
    : QDialog(parent), m_currentType(Serial), m_isAdvancedVisible(false), m_isTesting(false)
{
    setWindowTitle("连接设备");
    setModal(true);
    resize(500, 400);

    setupUI();
    loadHistory();
}

ConnectionDialog::~ConnectionDialog()
{
    saveHistory();
}

void ConnectionDialog::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);

    // 创建标签页
    m_tabWidget = new QTabWidget;

    setupSerialTab();
    setupSSHTab();
    setupADBTab();
    setupNetworkTab();
    setupFTPTab();
    setupHistoryTab();

    m_mainLayout->addWidget(m_tabWidget);

    setupButtons();
}

void ConnectionDialog::setupSerialTab()
{
    m_serialTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_serialTab);

    m_serialPortCombo = new QComboBox;
    m_refreshPortsBtn = new QPushButton("刷新");
    QHBoxLayout *portLayout = new QHBoxLayout;
    portLayout->addWidget(m_serialPortCombo);
    portLayout->addWidget(m_refreshPortsBtn);
    layout->addRow("串口:", portLayout);

    m_baudRateCombo = new QComboBox;
    m_baudRateCombo->addItems({"9600", "19200", "38400", "57600", "115200", "230400", "460800", "921600"});
    m_baudRateCombo->setCurrentText("115200");
    layout->addRow("波特率:", m_baudRateCombo);

    m_dataBitsCombo = new QComboBox;
    m_dataBitsCombo->addItems({"5", "6", "7", "8"});
    m_dataBitsCombo->setCurrentText("8");
    layout->addRow("数据位:", m_dataBitsCombo);

    m_stopBitsCombo = new QComboBox;
    m_stopBitsCombo->addItems({"1", "1.5", "2"});
    layout->addRow("停止位:", m_stopBitsCombo);

    m_parityCombo = new QComboBox;
    m_parityCombo->addItems({"无", "奇校验", "偶校验"});
    layout->addRow("校验位:", m_parityCombo);

    m_flowControlCombo = new QComboBox;
    m_flowControlCombo->addItems({"无", "硬件", "软件"});
    layout->addRow("流控制:", m_flowControlCombo);

    m_tabWidget->addTab(m_serialTab, "串口");

    connect(m_refreshPortsBtn, &QPushButton::clicked, this, &ConnectionDialog::onSerialPortRefresh);
}

void ConnectionDialog::setupSSHTab()
{
    m_sshTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_sshTab);

    m_sshHostEdit = new QLineEdit;
    layout->addRow("主机:", m_sshHostEdit);

    m_sshPortSpin = new QSpinBox;
    m_sshPortSpin->setRange(1, 65535);
    m_sshPortSpin->setValue(22);
    layout->addRow("端口:", m_sshPortSpin);

    m_sshUserEdit = new QLineEdit;
    layout->addRow("用户名:", m_sshUserEdit);

    m_sshPasswordEdit = new QLineEdit;
    m_sshPasswordEdit->setEchoMode(QLineEdit::Password);
    layout->addRow("密码:", m_sshPasswordEdit);

    m_sshUseKeyCheck = new QCheckBox("使用密钥认证");
    layout->addRow(m_sshUseKeyCheck);

    m_sshKeyFileEdit = new QLineEdit;
    m_sshKeyBrowseBtn = new QPushButton("浏览...");
    QHBoxLayout *keyLayout = new QHBoxLayout;
    keyLayout->addWidget(m_sshKeyFileEdit);
    keyLayout->addWidget(m_sshKeyBrowseBtn);
    layout->addRow("密钥文件:", keyLayout);

    m_tabWidget->addTab(m_sshTab, "SSH");
}

void ConnectionDialog::setupADBTab()
{
    m_adbTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_adbTab);

    m_adbDeviceCombo = new QComboBox;
    m_refreshADBBtn = new QPushButton("刷新");
    QHBoxLayout *deviceLayout = new QHBoxLayout;
    deviceLayout->addWidget(m_adbDeviceCombo);
    deviceLayout->addWidget(m_refreshADBBtn);
    layout->addRow("设备:", deviceLayout);

    m_adbCommandEdit = new QLineEdit;
    m_adbCommandEdit->setPlaceholderText("可选：自定义ADB路径");
    layout->addRow("ADB路径:", m_adbCommandEdit);

    m_tabWidget->addTab(m_adbTab, "ADB");

    connect(m_refreshADBBtn, &QPushButton::clicked, this, &ConnectionDialog::onADBDeviceRefresh);
}

void ConnectionDialog::setupNetworkTab()
{
    m_networkTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_networkTab);

    m_networkHostEdit = new QLineEdit;
    layout->addRow("主机:", m_networkHostEdit);

    m_networkPortSpin = new QSpinBox;
    m_networkPortSpin->setRange(1, 65535);
    m_networkPortSpin->setValue(8080);
    layout->addRow("端口:", m_networkPortSpin);

    m_networkProtocolCombo = new QComboBox;
    m_networkProtocolCombo->addItems({"TCP", "UDP"});
    layout->addRow("协议:", m_networkProtocolCombo);

    m_tabWidget->addTab(m_networkTab, "网络");
}

void ConnectionDialog::setupFTPTab()
{
    m_ftpTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_ftpTab);

    m_ftpHostEdit = new QLineEdit;
    layout->addRow("主机:", m_ftpHostEdit);

    m_ftpPortSpin = new QSpinBox;
    m_ftpPortSpin->setRange(1, 65535);
    m_ftpPortSpin->setValue(21);
    layout->addRow("端口:", m_ftpPortSpin);

    m_ftpUserEdit = new QLineEdit;
    layout->addRow("用户名:", m_ftpUserEdit);

    m_ftpPasswordEdit = new QLineEdit;
    m_ftpPasswordEdit->setEchoMode(QLineEdit::Password);
    layout->addRow("密码:", m_ftpPasswordEdit);

    m_ftpPassiveModeCheck = new QCheckBox("被动模式");
    m_ftpPassiveModeCheck->setChecked(true);
    layout->addRow(m_ftpPassiveModeCheck);

    m_tabWidget->addTab(m_ftpTab, "FTP");
}

void ConnectionDialog::setupHistoryTab()
{
    m_historyTab = new QWidget;
    QVBoxLayout *layout = new QVBoxLayout(m_historyTab);

    m_historyCombo = new QComboBox;
    layout->addWidget(m_historyCombo);

    QHBoxLayout *historyBtnLayout = new QHBoxLayout;
    m_saveHistoryBtn = new QPushButton("保存当前");
    m_deleteHistoryBtn = new QPushButton("删除选中");
    historyBtnLayout->addWidget(m_saveHistoryBtn);
    historyBtnLayout->addWidget(m_deleteHistoryBtn);
    historyBtnLayout->addStretch();
    layout->addLayout(historyBtnLayout);

    m_historyDetailsEdit = new QTextEdit;
    m_historyDetailsEdit->setReadOnly(true);
    layout->addWidget(m_historyDetailsEdit);

    m_tabWidget->addTab(m_historyTab, "历史记录");

    connect(m_historyCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ConnectionDialog::onHistoryItemSelected);
    connect(m_saveHistoryBtn, &QPushButton::clicked, this, &ConnectionDialog::onSaveToHistory);
    connect(m_deleteHistoryBtn, &QPushButton::clicked, this, &ConnectionDialog::onDeleteFromHistory);
}

void ConnectionDialog::setupButtons()
{
    QHBoxLayout *buttonLayout = new QHBoxLayout;

    m_testBtn = new QPushButton("测试连接");
    m_connectBtn = new QPushButton("连接");
    m_cancelBtn = new QPushButton("取消");
    m_advancedBtn = new QPushButton("高级选项");

    buttonLayout->addWidget(m_testBtn);
    buttonLayout->addWidget(m_advancedBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_connectBtn);
    buttonLayout->addWidget(m_cancelBtn);

    m_mainLayout->addLayout(buttonLayout);

    // 状态显示
    m_statusLabel = new QLabel;
    m_progressBar = new QProgressBar;
    m_progressBar->setVisible(false);

    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addWidget(m_progressBar);

    // 连接信号
    connect(m_testBtn, &QPushButton::clicked, this, &ConnectionDialog::testConnection);
    connect(m_connectBtn, &QPushButton::clicked, this, &ConnectionDialog::connectToDevice);
    connect(m_cancelBtn, &QPushButton::clicked, this, &QDialog::reject);
    connect(m_advancedBtn, &QPushButton::clicked, this, &ConnectionDialog::onAdvancedToggled);

    // 测试超时定时器
    m_testTimer = new QTimer(this);
    m_testTimer->setSingleShot(true);
    connect(m_testTimer, &QTimer::timeout, this, &ConnectionDialog::onTestTimeout);
}

void ConnectionDialog::setConnectionType(ConnectionType type)
{
    m_currentType = type;
    m_tabWidget->setCurrentIndex(static_cast<int>(type));
}

ConnectionDialog::ConnectionType ConnectionDialog::connectionType() const
{
    return static_cast<ConnectionType>(m_tabWidget->currentIndex());
}

QVariantMap ConnectionDialog::getConnectionParams() const
{
    QVariantMap params;

    ConnectionType type = connectionType();
    params["type"] = static_cast<int>(type);

    switch (type)
    {
    case Serial:
        params.unite(getSerialParams());
        break;
    case SSH:
        params.unite(getSSHParams());
        break;
    case ADB:
        params.unite(getADBParams());
        break;
    case Network:
        params.unite(getNetworkParams());
        break;
    case FTP:
        params.unite(getFTPParams());
        break;
    }

    return params;
}

void ConnectionDialog::setConnectionParams(const QVariantMap &params)
{
    ConnectionType type = static_cast<ConnectionType>(params.value("type", 0).toInt());
    setConnectionType(type);

    switch (type)
    {
    case Serial:
        setSerialParams(params);
        break;
    case SSH:
        setSSHParams(params);
        break;
    case ADB:
        setADBParams(params);
        break;
    case Network:
        setNetworkParams(params);
        break;
    case FTP:
        setFTPParams(params);
        break;
    }
}

// 简化的实现方法
QVariantMap ConnectionDialog::getSerialParams() const
{
    QVariantMap params;
    params["port"] = m_serialPortCombo->currentText();
    params["baud_rate"] = m_baudRateCombo->currentText().toInt();
    params["data_bits"] = m_dataBitsCombo->currentText().toInt();
    params["stop_bits"] = m_stopBitsCombo->currentText();
    params["parity"] = m_parityCombo->currentText();
    params["flow_control"] = m_flowControlCombo->currentText();
    return params;
}

QVariantMap ConnectionDialog::getSSHParams() const
{
    QVariantMap params;
    params["host"] = m_sshHostEdit->text();
    params["port"] = m_sshPortSpin->value();
    params["username"] = m_sshUserEdit->text();
    params["password"] = m_sshPasswordEdit->text();
    params["use_key"] = m_sshUseKeyCheck->isChecked();
    params["key_file"] = m_sshKeyFileEdit->text();
    return params;
}

QVariantMap ConnectionDialog::getADBParams() const
{
    QVariantMap params;
    params["device"] = m_adbDeviceCombo->currentText();
    params["adb_path"] = m_adbCommandEdit->text();
    return params;
}

QVariantMap ConnectionDialog::getNetworkParams() const
{
    QVariantMap params;
    params["host"] = m_networkHostEdit->text();
    params["port"] = m_networkPortSpin->value();
    params["protocol"] = m_networkProtocolCombo->currentText();
    return params;
}

QVariantMap ConnectionDialog::getFTPParams() const
{
    QVariantMap params;
    params["host"] = m_ftpHostEdit->text();
    params["port"] = m_ftpPortSpin->value();
    params["username"] = m_ftpUserEdit->text();
    params["password"] = m_ftpPasswordEdit->text();
    params["passive_mode"] = m_ftpPassiveModeCheck->isChecked();
    return params;
}

void ConnectionDialog::setSerialParams(const QVariantMap &params)
{
    m_serialPortCombo->setCurrentText(params.value("port").toString());
    m_baudRateCombo->setCurrentText(params.value("baud_rate", 115200).toString());
    m_dataBitsCombo->setCurrentText(params.value("data_bits", 8).toString());
    m_stopBitsCombo->setCurrentText(params.value("stop_bits", "1").toString());
    m_parityCombo->setCurrentText(params.value("parity", "无").toString());
    m_flowControlCombo->setCurrentText(params.value("flow_control", "无").toString());
}

void ConnectionDialog::setSSHParams(const QVariantMap &params)
{
    m_sshHostEdit->setText(params.value("host").toString());
    m_sshPortSpin->setValue(params.value("port", 22).toInt());
    m_sshUserEdit->setText(params.value("username").toString());
    m_sshPasswordEdit->setText(params.value("password").toString());
    m_sshUseKeyCheck->setChecked(params.value("use_key", false).toBool());
    m_sshKeyFileEdit->setText(params.value("key_file").toString());
}

void ConnectionDialog::setADBParams(const QVariantMap &params)
{
    m_adbDeviceCombo->setCurrentText(params.value("device").toString());
    m_adbCommandEdit->setText(params.value("adb_path").toString());
}

void ConnectionDialog::setNetworkParams(const QVariantMap &params)
{
    m_networkHostEdit->setText(params.value("host").toString());
    m_networkPortSpin->setValue(params.value("port", 8080).toInt());
    m_networkProtocolCombo->setCurrentText(params.value("protocol", "TCP").toString());
}

void ConnectionDialog::setFTPParams(const QVariantMap &params)
{
    m_ftpHostEdit->setText(params.value("host").toString());
    m_ftpPortSpin->setValue(params.value("port", 21).toInt());
    m_ftpUserEdit->setText(params.value("username").toString());
    m_ftpPasswordEdit->setText(params.value("password").toString());
    m_ftpPassiveModeCheck->setChecked(params.value("passive_mode", true).toBool());
}

void ConnectionDialog::testConnection()
{
    m_isTesting = true;
    m_testBtn->setEnabled(false);
    m_progressBar->setVisible(true);
    m_statusLabel->setText("正在测试连接...");

    QVariantMap params = getConnectionParams();
    emit testConnectionRequested(params);

    m_testTimer->start(10000); // 10秒超时
}

void ConnectionDialog::connectToDevice()
{
    QVariantMap params = getConnectionParams();
    emit connectionRequested(params);
    accept();
}

void ConnectionDialog::onTestResult(bool success, const QString &message)
{
    m_isTesting = false;
    m_testBtn->setEnabled(true);
    m_progressBar->setVisible(false);

    if (success)
    {
        m_statusLabel->setText("✅ " + message);
    }
    else
    {
        m_statusLabel->setText("❌ " + message);
    }
}

void ConnectionDialog::onSerialPortRefresh()
{
    m_serialPortCombo->clear();
    // 这里应该调用SerialConnection::getAvailablePorts()
    // 简化实现
    m_serialPortCombo->addItems({"COM1", "COM2", "COM3", "COM4"});
}

void ConnectionDialog::onADBDeviceRefresh()
{
    m_adbDeviceCombo->clear();
    // 这里应该调用ADBConnection::getAvailableDevices()
    // 简化实现
    m_adbDeviceCombo->addItems({"emulator-5554", "device1", "device2"});
}

void ConnectionDialog::onAdvancedToggled(bool show)
{
    // 简化实现 - 这里可以显示/隐藏高级选项
    m_isAdvancedVisible = !m_isAdvancedVisible;
    m_advancedBtn->setText(m_isAdvancedVisible ? "隐藏高级选项" : "显示高级选项");
}

void ConnectionDialog::onHistoryItemSelected()
{
    // 简化实现
    int index = m_historyCombo->currentIndex();
    if (index >= 0 && index < m_connectionHistory.size())
    {
        QVariantMap params = m_connectionHistory[index].toMap();
        setConnectionParams(params);

        QString details = QString("类型: %1\n主机: %2\n端口: %3")
                              .arg(params.value("type").toString())
                              .arg(params.value("host").toString())
                              .arg(params.value("port").toString());
        m_historyDetailsEdit->setText(details);
    }
}

void ConnectionDialog::onSaveToHistory()
{
    QVariantMap params = getConnectionParams();
    addToHistory(params);
}

void ConnectionDialog::onDeleteFromHistory()
{
    int index = m_historyCombo->currentIndex();
    if (index >= 0 && index < m_connectionHistory.size())
    {
        m_connectionHistory.removeAt(index);
        m_historyCombo->removeItem(index);
        saveHistory();
    }
}

void ConnectionDialog::onTestTimeout()
{
    if (m_isTesting)
    {
        onTestResult(false, "测试连接超时");
    }
}

void ConnectionDialog::addToHistory(const QVariantMap &params)
{
    m_connectionHistory.prepend(QVariant::fromValue(params));

    // 限制历史记录数量
    while (m_connectionHistory.size() > 20)
    {
        m_connectionHistory.removeLast();
    }

    saveHistory();
    loadHistory();
}

void ConnectionDialog::loadHistory()
{
    // 简化实现 - 实际应该从配置文件加载
    m_historyCombo->clear();
    for (int i = 0; i < m_connectionHistory.size(); ++i)
    {
        QVariantMap params = m_connectionHistory[i].toMap();
        QString name = QString("%1:%2").arg(params.value("host").toString()).arg(params.value("port").toString());
        m_historyCombo->addItem(name);
    }
}

void ConnectionDialog::saveHistory()
{
    // 简化实现 - 实际应该保存到配置文件
}
