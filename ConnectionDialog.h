#ifndef CONNECTIONDIALOG_H
#define CONNECTIONDIALOG_H

#include <QDialog>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGridLayout>
#include <QLabel>
#include <QLineEdit>
#include <QComboBox>
#include <QSpinBox>
#include <QCheckBox>
#include <QPushButton>
#include <QGroupBox>
#include <QTextEdit>
#include <QProgressBar>
#include <QTimer>
#include <QVariantMap>

class ConnectionDialog : public QDialog
{
    Q_OBJECT

public:
    enum ConnectionType
    {
        Serial,
        TCP,
        ADB,
        Network,
        FTP
    };

    explicit ConnectionDialog(QWidget *parent = nullptr);
    ~ConnectionDialog();

    // 连接配置
    void setConnectionType(ConnectionType type);
    ConnectionType connectionType() const;
    QVariantMap getConnectionParams() const;
    void setConnectionParams(const QVariantMap &params);

    // 历史记录
    void addToHistory(const QVariantMap &params);
    void loadHistory();
    void saveHistory();

public slots:
    void testConnection();
    void connectToDevice();
    void onTestResult(bool success, const QString &message);
    void refreshCurrentDevices();

signals:
    void connectionRequested(const QVariantMap &params);
    void testConnectionRequested(const QVariantMap &params);

private slots:
    void onConnectionTypeChanged();
    void onSerialPortRefresh();
    void onADBDeviceRefresh();
    void onAdvancedToggled(bool show);
    void onHistoryItemSelected();
    void onSaveToHistory();
    void onDeleteFromHistory();
    void onTestTimeout();

private:
    void setupUI();
    void setupSerialTab();
    void setupTCPTab();
    void setupADBTab();
    void setupNetworkTab();
    void setupFTPTab();
    void setupHistoryTab();
    void setupButtons();

    void updateUI();
    void updateSerialPorts();
    void updateADBDevices();
    void validateInput();

    // 设备检测方法
    QStringList getAvailableSerialPorts();
    QStringList getAvailableADBDevices();

    QVariantMap getSerialParams() const;
    QVariantMap getTCPParams() const;
    QVariantMap getADBParams() const;
    QVariantMap getNetworkParams() const;
    QVariantMap getFTPParams() const;

    void setSerialParams(const QVariantMap &params);
    void setTCPParams(const QVariantMap &params);
    void setADBParams(const QVariantMap &params);
    void setNetworkParams(const QVariantMap &params);
    void setFTPParams(const QVariantMap &params);

protected:
    void showEvent(QShowEvent *event) override;

private:
    // 主界面
    QTabWidget *m_tabWidget;
    QVBoxLayout *m_mainLayout;

    // 串口配置
    QWidget *m_serialTab;
    QComboBox *m_serialPortCombo;
    QComboBox *m_baudRateCombo;
    QComboBox *m_dataBitsCombo;
    QComboBox *m_stopBitsCombo;
    QComboBox *m_parityCombo;
    QComboBox *m_flowControlCombo;
    QPushButton *m_refreshPortsBtn;

    // TCP配置
    QWidget *m_tcpTab;
    QLineEdit *m_tcpHostEdit;
    QSpinBox *m_tcpPortSpin;
    QComboBox *m_tcpProtocolCombo;
    QLineEdit *m_tcpLineEndingEdit;
    QComboBox *m_tcpEncodingCombo;
    QCheckBox *m_tcpKeepAliveCheck;
    QSpinBox *m_tcpTimeoutSpin;

    // ADB配置
    QWidget *m_adbTab;
    QComboBox *m_adbDeviceCombo;
    QPushButton *m_refreshADBBtn;
    QLineEdit *m_adbCommandEdit;

    // 网络配置
    QWidget *m_networkTab;
    QLineEdit *m_networkHostEdit;
    QSpinBox *m_networkPortSpin;
    QComboBox *m_networkProtocolCombo;

    // FTP配置
    QWidget *m_ftpTab;
    QLineEdit *m_ftpHostEdit;
    QSpinBox *m_ftpPortSpin;
    QLineEdit *m_ftpUserEdit;
    QLineEdit *m_ftpPasswordEdit;
    QCheckBox *m_ftpPassiveModeCheck;

    // 高级选项
    QGroupBox *m_advancedGroup;
    QCheckBox *m_autoLoginCheck;
    QLineEdit *m_loginUserEdit;
    QLineEdit *m_loginPasswordEdit;
    QSpinBox *m_timeoutSpin;
    QCheckBox *m_autoReconnectCheck;

    // 历史记录
    QWidget *m_historyTab;
    QComboBox *m_historyCombo;
    QPushButton *m_saveHistoryBtn;
    QPushButton *m_deleteHistoryBtn;
    QTextEdit *m_historyDetailsEdit;

    // 按钮
    QPushButton *m_testBtn;
    QPushButton *m_connectBtn;
    QPushButton *m_cancelBtn;
    QPushButton *m_advancedBtn;

    // 状态
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    QTimer *m_testTimer;

    // 数据
    ConnectionType m_currentType;
    QVariantList m_connectionHistory;
    bool m_isAdvancedVisible;
    bool m_isTesting;
};

#endif // CONNECTIONDIALOG_H
