#include "ConnectionManager.h"
#include "SerialConnection.h"
#include "SSHConnection.h"
#include "ADBConnection.h"
#include <QMutexLocker>
#include <QDebug>
#include <QTimer>

ConnectionManager::ConnectionManager(QObject *parent)
    : QObject(parent), m_serialConnection(nullptr), m_sshConnection(nullptr), m_adbConnection(nullptr), m_networkConnection(nullptr), m_ftpConnection(nullptr), m_currentType(None), m_currentStatus(Disconnected), m_connectionTimeout(30), m_autoReconnectEnabled(false), m_reconnectAttempts(0), m_maxReconnectAttempts(3)
{
    // 初始化重连定时器
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &ConnectionManager::onReconnectTimer);

    initializeConnections();
}

ConnectionManager::~ConnectionManager()
{
    cleanupConnections();
}

void ConnectionManager::initializeConnections()
{
    // 创建串口连接对象
    m_serialConnection = new SerialConnection(this);
    QObject::connect(m_serialConnection, &SerialConnection::connected,
                     this, &ConnectionManager::onConnectionStatusChanged);
    QObject::connect(m_serialConnection, &SerialConnection::disconnected,
                     this, &ConnectionManager::onConnectionStatusChanged);
    QObject::connect(m_serialConnection, &SerialConnection::dataReceived,
                     this, &ConnectionManager::onDataReceived);
    QObject::connect(m_serialConnection, &SerialConnection::errorOccurred,
                     this, &ConnectionManager::onErrorOccurred);

    // TODO: 初始化其他连接类型
    // m_sshConnection = new SSHConnection(this);
    // m_adbConnection = new ADBConnection(this);
    // m_networkConnection = new NetworkConnection(this);
    // m_ftpConnection = new FTPConnection(this);
}

void ConnectionManager::cleanupConnections()
{
    if (isConnected())
    {
        disconnectFromDevice();
    }

    // 清理连接对象
    if (m_serialConnection)
    {
        m_serialConnection->deleteLater();
        m_serialConnection = nullptr;
    }

    // TODO: 清理其他连接类型
}

bool ConnectionManager::connectToDevice(const QVariantMap &params)
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus == Connected || m_currentStatus == Connecting)
    {
        disconnectFromDevice();
    }

    setStatus(Connecting, "正在连接...");

    QString typeStr = params.value("type", "serial").toString().toLower();
    ConnectionType type = stringToConnectionType(typeStr);

    m_connectionParams = params;
    m_currentType = type;

    bool success = createConnection(type, params);

    if (success)
    {
        setStatus(Connected, "连接成功");
        m_reconnectAttempts = 0;
        emit connectionEstablished();
    }
    else
    {
        setStatus(Error, "连接失败");
        m_currentType = None;
        m_connectionParams.clear();
    }

    return success;
}

void ConnectionManager::disconnectFromDevice()
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus == Disconnected)
    {
        return;
    }

    setStatus(Disconnecting, "正在断开连接...");

    // 停止重连定时器
    if (m_reconnectTimer->isActive())
    {
        m_reconnectTimer->stop();
    }

    destroyCurrentConnection();

    setStatus(Disconnected, "已断开连接");
    m_currentType = None;
    m_connectionParams.clear();

    emit connectionLost();
}

bool ConnectionManager::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentStatus == Connected;
}

ConnectionManager::ConnectionType ConnectionManager::currentConnectionType() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentType;
}

ConnectionManager::ConnectionStatus ConnectionManager::currentStatus() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentStatus;
}

QString ConnectionManager::currentStatusString() const
{
    QMutexLocker locker(&m_mutex);

    switch (m_currentStatus)
    {
    case Disconnected:
        return "未连接";
    case Connecting:
        return "连接中";
    case Connected:
        return "已连接";
    case Disconnecting:
        return "断开中";
    case Error:
        return "错误";
    default:
        return "未知状态";
    }
}

QVariantMap ConnectionManager::currentConnectionParams() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionParams;
}

bool ConnectionManager::sendCommand(const QString &command)
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus != Connected)
    {
        return false;
    }

    bool success = false;

    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            success = m_serialConnection->sendCommand(command);
        }
        break;
    case SSH:
        // TODO: 实现SSH命令发送
        break;
    case ADB:
        // TODO: 实现ADB命令发送
        break;
    case Network:
        // TODO: 实现网络命令发送
        break;
    case FTP:
        // TODO: 实现FTP命令发送
        break;
    default:
        break;
    }

    return success;
}

bool ConnectionManager::sendData(const QByteArray &data)
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus != Connected)
    {
        return false;
    }

    bool success = false;

    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            success = m_serialConnection->sendData(data);
        }
        break;
    case SSH:
        // TODO: 实现SSH数据发送
        break;
    case ADB:
        // TODO: 实现ADB数据发送
        break;
    case Network:
        // TODO: 实现网络数据发送
        break;
    case FTP:
        // TODO: 实现FTP数据发送
        break;
    default:
        break;
    }

    return success;
}

QString ConnectionManager::getConnectionInfo() const
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus != Connected)
    {
        return "未连接";
    }

    QString info = QString("连接类型: %1\n").arg(connectionTypeToString(m_currentType));

    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            info += QString("串口: %1\n").arg(m_connectionParams.value("port").toString());
            info += QString("波特率: %1\n").arg(m_connectionParams.value("baud_rate").toInt());
        }
        break;
    case SSH:
        info += QString("主机: %1:%2\n")
                    .arg(m_connectionParams.value("host").toString())
                    .arg(m_connectionParams.value("port").toInt());
        break;
    case ADB:
        info += QString("设备: %1\n").arg(m_connectionParams.value("device").toString());
        break;
    case Network:
        info += QString("地址: %1:%2\n")
                    .arg(m_connectionParams.value("host").toString())
                    .arg(m_connectionParams.value("port").toInt());
        break;
    case FTP:
        info += QString("FTP服务器: %1:%2\n")
                    .arg(m_connectionParams.value("host").toString())
                    .arg(m_connectionParams.value("port").toInt());
        break;
    default:
        break;
    }

    return info;
}

QStringList ConnectionManager::getAvailablePorts() const
{
    return SerialConnection::getAvailablePorts();
}

QStringList ConnectionManager::getAvailableADBDevices() const
{
    // TODO: 实现ADB设备列表获取
    QStringList devices;
    devices << "模拟器设备1" << "模拟器设备2";
    return devices;
}

void ConnectionManager::setConnectionTimeout(int seconds)
{
    m_connectionTimeout = qMax(1, seconds);
}

int ConnectionManager::connectionTimeout() const
{
    return m_connectionTimeout;
}

void ConnectionManager::setAutoReconnect(bool enabled)
{
    m_autoReconnectEnabled = enabled;
}

bool ConnectionManager::autoReconnectEnabled() const
{
    return m_autoReconnectEnabled;
}

void ConnectionManager::reconnect()
{
    if (!m_connectionParams.isEmpty())
    {
        connectToDevice(m_connectionParams);
    }
}

void ConnectionManager::testConnection(const QVariantMap &params)
{
    // 简单的连接测试
    bool success = false;
    QString message;

    QString typeStr = params.value("type", "serial").toString().toLower();
    ConnectionType type = stringToConnectionType(typeStr);

    switch (type)
    {
    case Serial:
    {
        QString port = params.value("port").toString();
        if (!port.isEmpty())
        {
            success = true;
            message = QString("串口 %1 可用").arg(port);
        }
        else
        {
            message = "串口参数无效";
        }
        break;
    }
    case SSH:
    {
        QString host = params.value("host").toString();
        int port = params.value("port", 22).toInt();
        if (!host.isEmpty() && port > 0)
        {
            success = true;
            message = QString("SSH连接参数有效: %1:%2").arg(host).arg(port);
        }
        else
        {
            message = "SSH参数无效";
        }
        break;
    }
    default:
        message = "不支持的连接类型";
        break;
    }

    emit testResult(success, message);
}

void ConnectionManager::onConnectionStatusChanged()
{
    // 处理连接状态变化
    QObject *sender = this->sender();

    if (sender == m_serialConnection)
    {
        if (m_serialConnection->isConnected())
        {
            setStatus(Connected, "串口连接成功");
        }
        else
        {
            setStatus(Disconnected, "串口连接断开");

            // 如果启用自动重连，尝试重连
            if (m_autoReconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts)
            {
                m_reconnectAttempts++;
                m_reconnectTimer->start(5000); // 5秒后重连
            }
        }
    }
    // TODO: 处理其他连接类型的状态变化
}

void ConnectionManager::onDataReceived(const QByteArray &data)
{
    QString dataStr = QString::fromUtf8(data);

    // 添加到数据缓冲区
    m_dataBuffer.append(data);

    // 处理完整的行
    while (m_dataBuffer.contains('\n'))
    {
        int index = m_dataBuffer.indexOf('\n');
        QString line = QString::fromUtf8(m_dataBuffer.left(index)).trimmed();
        m_dataBuffer.remove(0, index + 1);

        if (!line.isEmpty())
        {
            emit dataReceived(line);
        }
    }

    // 如果缓冲区太大，清理一部分
    if (m_dataBuffer.size() > 10240)
    {                                            // 10KB
        m_dataBuffer = m_dataBuffer.right(5120); // 保留后5KB
    }
}

void ConnectionManager::onErrorOccurred(const QString &error)
{
    setStatus(Error, error);
    emit errorOccurred(error);

    // 如果启用自动重连，尝试重连
    if (m_autoReconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts)
    {
        m_reconnectAttempts++;
        m_reconnectTimer->start(10000); // 10秒后重连
    }
}

void ConnectionManager::onReconnectTimer()
{
    if (m_autoReconnectEnabled && !m_connectionParams.isEmpty())
    {
        setStatus(Connecting, QString("自动重连中... (第%1次)").arg(m_reconnectAttempts));
        connectToDevice(m_connectionParams);
    }
}

void ConnectionManager::setStatus(ConnectionStatus status, const QString &details)
{
    if (m_currentStatus != status)
    {
        m_currentStatus = status;
        m_statusDetails = details;

        QString statusStr = currentStatusString();
        emit statusChanged(statusStr, details);
    }
}

bool ConnectionManager::createConnection(ConnectionType type, const QVariantMap &params)
{
    bool success = false;

    switch (type)
    {
    case Serial:
        if (m_serialConnection)
        {
            success = m_serialConnection->connect(params);
        }
        break;
    case SSH:
        // TODO: 实现SSH连接创建
        break;
    case ADB:
        // TODO: 实现ADB连接创建
        break;
    case Network:
        // TODO: 实现网络连接创建
        break;
    case FTP:
        // TODO: 实现FTP连接创建
        break;
    default:
        break;
    }

    return success;
}

void ConnectionManager::destroyCurrentConnection()
{
    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            m_serialConnection->disconnect();
        }
        break;
    case SSH:
        // TODO: 断开SSH连接
        break;
    case ADB:
        // TODO: 断开ADB连接
        break;
    case Network:
        // TODO: 断开网络连接
        break;
    case FTP:
        // TODO: 断开FTP连接
        break;
    default:
        break;
    }
}

QString ConnectionManager::connectionTypeToString(ConnectionType type) const
{
    switch (type)
    {
    case Serial:
        return "串口";
    case SSH:
        return "SSH";
    case ADB:
        return "ADB";
    case Network:
        return "网络";
    case FTP:
        return "FTP";
    default:
        return "未知";
    }
}

ConnectionManager::ConnectionType ConnectionManager::stringToConnectionType(const QString &typeStr) const
{
    QString lower = typeStr.toLower();

    if (lower == "serial" || lower == "串口")
    {
        return Serial;
    }
    else if (lower == "ssh")
    {
        return SSH;
    }
    else if (lower == "adb")
    {
        return ADB;
    }
    else if (lower == "network" || lower == "网络")
    {
        return Network;
    }
    else if (lower == "ftp")
    {
        return FTP;
    }
    else
    {
        return None;
    }
}
