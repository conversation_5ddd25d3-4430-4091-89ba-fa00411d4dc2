#include "ADBConnection.h"
#include <QMutexLocker>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QFileInfo>
#include <QRegularExpression>
#include <QProcess>
#include <QTimer>
#include <QDateTime>

ADBConnection::ADBConnection(QObject *parent)
    : QObject(parent), m_adbProcess(nullptr), m_adbPath("adb"), m_state(Disconnected), m_isConnected(false), m_connectionTimeout(30), m_autoReconnectEnabled(false), m_bytesReceived(0), m_bytesSent(0), m_commandCount(0), m_isExecutingCommand(false)
{
    // 尝试找到ADB路径
    QString foundPath = findAdbPath();
    if (!foundPath.isEmpty())
    {
        m_adbPath = foundPath;
    }

    // 创建ADB进程
    m_adbProcess = new QProcess(this);
    QObject::connect(m_adbProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                     this, &ADBConnection::onProcessFinished);
    QObject::connect(m_adbProcess, &QProcess::errorOccurred, this, &ADBConnection::onProcessError);
    QObject::connect(m_adbProcess, &QProcess::readyReadStandardOutput, this, &ADBConnection::onProcessReadyRead);
    QObject::connect(m_adbProcess, &QProcess::readyReadStandardError, this, &ADBConnection::onProcessReadyRead);

    // 创建定时器
    m_connectionTimer = new QTimer(this);
    m_connectionTimer->setSingleShot(true);
    QObject::connect(m_connectionTimer, &QTimer::timeout, this, &ADBConnection::onConnectionTimeout);

    m_deviceCheckTimer = new QTimer(this);
    m_deviceCheckTimer->setInterval(5000); // 每5秒检查一次设备状态
    QObject::connect(m_deviceCheckTimer, &QTimer::timeout, this, &ADBConnection::onDeviceCheckTimer);

    // 初始化设备信息
    m_deviceInfo.state = Unknown;
    m_deviceInfo.isEmulator = false;
}

ADBConnection::~ADBConnection()
{
    disconnect();
}

bool ADBConnection::connect(const QVariantMap &params)
{
    QMutexLocker locker(&m_mutex);

    if (m_isConnected || m_state == Connecting)
    {
        return false;
    }

    // 解析连接参数
    m_deviceId = params.value("device", "").toString();
    m_adbPath = params.value("adb_path", m_adbPath).toString();

    // 检查ADB是否可用
    if (!checkAdbAvailable())
    {
        emit errorOccurred("ADB不可用，请检查ADB路径配置");
        return false;
    }

    setState(Connecting);
    m_connectionTimer->start(m_connectionTimeout * 1000);

    // 如果没有指定设备，获取设备列表
    if (m_deviceId.isEmpty())
    {
        QStringList devices = getAvailableDevices();
        if (devices.isEmpty())
        {
            setState(Error);
            emit errorOccurred("没有找到可用的Android设备");
            return false;
        }
        m_deviceId = devices.first();
    }

    // 检查设备连接
    if (checkDeviceConnected())
    {
        setState(Connected);
        m_isConnected = true;
        m_connectionTime = QDateTime::currentDateTime();

        // 获取设备信息
        refreshDeviceInfo();

        // 开始设备状态监控
        m_deviceCheckTimer->start();

        emit connected();
        return true;
    }
    else
    {
        setState(Error);
        emit errorOccurred(QString("无法连接到设备: %1").arg(m_deviceId));
        return false;
    }
}

void ADBConnection::disconnect()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isConnected && m_state == Disconnected)
    {
        return;
    }

    cleanup();

    setState(Disconnected);
    m_isConnected = false;

    emit disconnected();
}

bool ADBConnection::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_isConnected && m_deviceInfo.state == Device;
}

ADBConnection::ConnectionState ADBConnection::state() const
{
    QMutexLocker locker(&m_mutex);
    return m_state;
}

QString ADBConnection::stateString() const
{
    switch (state())
    {
    case Disconnected:
        return "未连接";
    case Connecting:
        return "连接中";
    case Connected:
        return "已连接";
    case Error:
        return "错误";
    default:
        return "未知状态";
    }
}

bool ADBConnection::sendCommand(const QString &command)
{
    return executeShellCommand(command);
}

bool ADBConnection::executeShellCommand(const QString &command)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << command;

    return executeAdbCommand(args);
}

bool ADBConnection::executeAdbCommand(const QStringList &args)
{
    QMutexLocker locker(&m_mutex);

    if (m_isExecutingCommand)
    {
        m_commandQueue.append(args.join(" "));
        return true;
    }

    m_isExecutingCommand = true;
    m_currentCommand = args.join(" ");

    bool success = startAdbProcess(args);
    if (success)
    {
        m_commandCount++;
    }
    else
    {
        m_isExecutingCommand = false;
    }

    return success;
}

void ADBConnection::setDevice(const QString &deviceId)
{
    QMutexLocker locker(&m_mutex);

    if (m_deviceId != deviceId)
    {
        bool wasConnected = m_isConnected;

        if (wasConnected)
        {
            disconnect();
        }

        m_deviceId = deviceId;

        if (wasConnected)
        {
            // 重新连接到新设备
            QVariantMap params;
            params["device"] = deviceId;
            connect(params);
        }
    }
}

QString ADBConnection::device() const
{
    QMutexLocker locker(&m_mutex);
    return m_deviceId;
}

ADBConnection::DeviceInfo ADBConnection::deviceInfo() const
{
    QMutexLocker locker(&m_mutex);
    return m_deviceInfo;
}

QStringList ADBConnection::getAvailableDevices()
{
    QStringList devices;

    QProcess process;
    process.start("adb", QStringList() << "devices");
    if (process.waitForFinished(5000))
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (const QString &line : lines)
        {
            if (line.contains('\t') && !line.startsWith("List of devices"))
            {
                QString deviceId = line.split('\t').first().trimmed();
                if (!deviceId.isEmpty())
                {
                    devices.append(deviceId);
                }
            }
        }
    }

    return devices;
}

QList<ADBConnection::DeviceInfo> ADBConnection::getDeviceList()
{
    QList<DeviceInfo> deviceList;

    QProcess process;
    process.start("adb", QStringList() << "devices" << "-l");
    if (process.waitForFinished(5000))
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (const QString &line : lines)
        {
            if (line.contains('\t') && !line.startsWith("List of devices"))
            {
                DeviceInfo info;
                QStringList parts = line.split(QRegularExpression("\\s+"));
                if (parts.size() >= 2)
                {
                    info.serialNumber = parts[0];
                    info.state = parseDeviceState(parts[1]);

                    // 解析设备属性
                    for (int i = 2; i < parts.size(); ++i)
                    {
                        QString part = parts[i];
                        if (part.startsWith("model:"))
                        {
                            info.model = part.mid(6);
                        }
                        else if (part.startsWith("product:"))
                        {
                            info.product = part.mid(8);
                        }
                        else if (part.startsWith("device:"))
                        {
                            info.device = part.mid(7);
                        }
                    }

                    info.isEmulator = info.serialNumber.startsWith("emulator-");
                    deviceList.append(info);
                }
            }
        }
    }

    return deviceList;
}

void ADBConnection::setAdbPath(const QString &path)
{
    QMutexLocker locker(&m_mutex);
    m_adbPath = path;
}

QString ADBConnection::adbPath() const
{
    QMutexLocker locker(&m_mutex);
    return m_adbPath;
}

QString ADBConnection::findAdbPath()
{
    // 常见的ADB路径
    QStringList possiblePaths = {
        "adb",
        "adb.exe",
        QDir::homePath() + "/Android/Sdk/platform-tools/adb",
        QDir::homePath() + "/Android/Sdk/platform-tools/adb.exe",
        "/usr/bin/adb",
        "/usr/local/bin/adb",
        "C:/Android/Sdk/platform-tools/adb.exe",
        "C:/Program Files/Android/Android Studio/bin/adb.exe"};

    // 检查环境变量
    QString androidHome = qgetenv("ANDROID_HOME");
    if (!androidHome.isEmpty())
    {
        possiblePaths.prepend(androidHome + "/platform-tools/adb");
        possiblePaths.prepend(androidHome + "/platform-tools/adb.exe");
    }

    QString androidSdkRoot = qgetenv("ANDROID_SDK_ROOT");
    if (!androidSdkRoot.isEmpty())
    {
        possiblePaths.prepend(androidSdkRoot + "/platform-tools/adb");
        possiblePaths.prepend(androidSdkRoot + "/platform-tools/adb.exe");
    }

    for (const QString &path : possiblePaths)
    {
        QProcess process;
        process.start(path, QStringList() << "version");
        if (process.waitForFinished(3000) && process.exitCode() == 0)
        {
            return path;
        }
    }

    return QString();
}

void ADBConnection::setConnectionTimeout(int seconds)
{
    m_connectionTimeout = qMax(1, seconds);
}

int ADBConnection::connectionTimeout() const
{
    return m_connectionTimeout;
}

void ADBConnection::setAutoReconnect(bool enabled)
{
    m_autoReconnectEnabled = enabled;
}

bool ADBConnection::autoReconnectEnabled() const
{
    return m_autoReconnectEnabled;
}

bool ADBConnection::pushFile(const QString &localPath, const QString &remotePath)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "push" << localPath << remotePath;

    return executeAdbCommand(args);
}

bool ADBConnection::pullFile(const QString &remotePath, const QString &localPath)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "pull" << remotePath << localPath;

    return executeAdbCommand(args);
}

bool ADBConnection::installApk(const QString &apkPath)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "install" << apkPath;

    return executeAdbCommand(args);
}

bool ADBConnection::uninstallPackage(const QString &packageName)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "uninstall" << packageName;

    return executeAdbCommand(args);
}

QString ADBConnection::getProperty(const QString &property)
{
    if (!isConnected())
    {
        return QString();
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "getprop" << property;

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(5000))
    {
        return process.readAllStandardOutput().trimmed();
    }

    return QString();
}

QVariantMap ADBConnection::getSystemProperties()
{
    QVariantMap properties;

    if (!isConnected())
    {
        return properties;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "getprop";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(10000))
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        QRegularExpression regex(R"(\[([^\]]+)\]:\s*\[([^\]]*)\])");
        for (const QString &line : lines)
        {
            QRegularExpressionMatch match = regex.match(line);
            if (match.hasMatch())
            {
                QString key = match.captured(1);
                QString value = match.captured(2);
                properties[key] = value;
            }
        }
    }

    return properties;
}

QStringList ADBConnection::getInstalledPackages()
{
    QStringList packages;

    if (!isConnected())
    {
        return packages;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "pm" << "list" << "packages";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(10000))
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (const QString &line : lines)
        {
            if (line.startsWith("package:"))
            {
                packages.append(line.mid(8).trimmed());
            }
        }
    }

    return packages;
}

QStringList ADBConnection::getRunningProcesses()
{
    QStringList processes;

    if (!isConnected())
    {
        return processes;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "ps";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(5000))
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (int i = 1; i < lines.size(); ++i)
        { // 跳过标题行
            QString line = lines[i].trimmed();
            if (!line.isEmpty())
            {
                processes.append(line);
            }
        }
    }

    return processes;
}

qint64 ADBConnection::bytesReceived() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesReceived;
}

qint64 ADBConnection::bytesSent() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesSent;
}

QDateTime ADBConnection::connectionTime() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionTime;
}

int ADBConnection::commandCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_commandCount;
}

void ADBConnection::reconnect()
{
    disconnect();

    QVariantMap params;
    params["device"] = m_deviceId;
    params["adb_path"] = m_adbPath;

    connect(params);
}

void ADBConnection::refreshDeviceInfo()
{
    if (!isConnected())
    {
        return;
    }

    // 获取设备基本信息
    m_deviceInfo.serialNumber = m_deviceId;
    m_deviceInfo.model = getProperty("ro.product.model");
    m_deviceInfo.product = getProperty("ro.product.name");
    m_deviceInfo.device = getProperty("ro.product.device");
    m_deviceInfo.androidVersion = getProperty("ro.build.version.release");
    m_deviceInfo.apiLevel = getProperty("ro.build.version.sdk");
    m_deviceInfo.isEmulator = m_deviceId.startsWith("emulator-");
    m_deviceInfo.state = Device;
}

// 私有槽函数实现
void ADBConnection::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    QMutexLocker locker(&m_mutex);

    QString output = m_adbProcess->readAllStandardOutput();
    QString error = m_adbProcess->readAllStandardError();

    if (!output.isEmpty())
    {
        m_bytesReceived += output.toUtf8().size();
        emit dataReceived(output.toUtf8());
    }

    if (!error.isEmpty())
    {
        emit errorOccurred(error);
    }

    emit commandFinished(exitCode, output);

    m_isExecutingCommand = false;

    // 处理命令队列
    if (!m_commandQueue.isEmpty())
    {
        QString nextCommand = m_commandQueue.takeFirst();
        QStringList args = nextCommand.split(' ');
        executeAdbCommand(args);
    }
}

void ADBConnection::onProcessError(QProcess::ProcessError error)
{
    QMutexLocker locker(&m_mutex);

    QString errorString;
    switch (error)
    {
    case QProcess::FailedToStart:
        errorString = "ADB进程启动失败";
        break;
    case QProcess::Crashed:
        errorString = "ADB进程崩溃";
        break;
    case QProcess::Timedout:
        errorString = "ADB进程超时";
        break;
    case QProcess::WriteError:
        errorString = "ADB进程写入错误";
        break;
    case QProcess::ReadError:
        errorString = "ADB进程读取错误";
        break;
    default:
        errorString = "ADB进程未知错误";
        break;
    }

    setState(Error);
    emit errorOccurred(formatError(errorString));

    m_isExecutingCommand = false;
}

void ADBConnection::onProcessReadyRead()
{
    processOutput();
}

void ADBConnection::onConnectionTimeout()
{
    QMutexLocker locker(&m_mutex);

    if (m_state == Connecting)
    {
        setState(Error);
        emit errorOccurred("连接超时");
    }
}

void ADBConnection::onDeviceCheckTimer()
{
    if (m_isConnected)
    {
        // 检查设备是否仍然连接
        if (!checkDeviceConnected())
        {
            setState(Error);
            m_isConnected = false;
            emit errorOccurred("设备连接丢失");

            if (m_autoReconnectEnabled)
            {
                QTimer::singleShot(2000, this, &ADBConnection::reconnect);
            }
        }
    }
}

// 私有方法实现
void ADBConnection::setState(ConnectionState state)
{
    if (m_state != state)
    {
        m_state = state;
        emit stateChanged(state);
    }
}

bool ADBConnection::startAdbProcess(const QStringList &arguments)
{
    if (m_adbProcess->state() != QProcess::NotRunning)
    {
        return false;
    }

    m_adbProcess->start(m_adbPath, arguments);
    return m_adbProcess->waitForStarted(3000);
}

void ADBConnection::processOutput()
{
    QByteArray data = m_adbProcess->readAllStandardOutput();
    if (!data.isEmpty())
    {
        m_outputBuffer.append(data);
        m_bytesReceived += data.size();
        emit dataReceived(data);
    }

    QByteArray errorData = m_adbProcess->readAllStandardError();
    if (!errorData.isEmpty())
    {
        m_errorBuffer.append(errorData);
    }
}

bool ADBConnection::checkAdbAvailable()
{
    QProcess process;
    process.start(m_adbPath, QStringList() << "version");
    return process.waitForFinished(3000) && process.exitCode() == 0;
}

bool ADBConnection::checkDeviceConnected()
{
    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "get-state";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(5000))
    {
        QString output = process.readAllStandardOutput().trimmed();
        return output == "device";
    }

    return false;
}

ADBConnection::DeviceInfo ADBConnection::parseDeviceInfo(const QString &deviceLine)
{
    DeviceInfo info;
    QStringList parts = deviceLine.split(QRegularExpression("\\s+"));

    if (parts.size() >= 2)
    {
        info.serialNumber = parts[0];
        info.state = parseDeviceState(parts[1]);
        info.isEmulator = info.serialNumber.startsWith("emulator-");

        // 解析额外属性
        for (int i = 2; i < parts.size(); ++i)
        {
            QString part = parts[i];
            if (part.startsWith("model:"))
            {
                info.model = part.mid(6);
            }
            else if (part.startsWith("product:"))
            {
                info.product = part.mid(8);
            }
            else if (part.startsWith("device:"))
            {
                info.device = part.mid(7);
            }
        }
    }

    return info;
}

ADBConnection::DeviceState ADBConnection::parseDeviceState(const QString &stateStr)
{
    QString state = stateStr.toLower();

    if (state == "device")
    {
        return Device;
    }
    else if (state == "offline")
    {
        return Offline;
    }
    else if (state == "unauthorized")
    {
        return Unauthorized;
    }
    else if (state == "no permissions")
    {
        return NoPermissions;
    }
    else if (state == "bootloader")
    {
        return Bootloader;
    }
    else if (state == "recovery")
    {
        return Recovery;
    }
    else
    {
        return Unknown;
    }
}

QString ADBConnection::formatError(const QString &error) const
{
    return QString("ADB连接错误 [%1]: %2").arg(m_deviceId.isEmpty() ? "未指定设备" : m_deviceId).arg(error);
}

void ADBConnection::cleanup()
{
    m_connectionTimer->stop();
    m_deviceCheckTimer->stop();

    if (m_adbProcess && m_adbProcess->state() != QProcess::NotRunning)
    {
        m_adbProcess->kill();
        m_adbProcess->waitForFinished(3000);
    }

    m_outputBuffer.clear();
    m_errorBuffer.clear();
    m_commandQueue.clear();
    m_isExecutingCommand = false;
}
