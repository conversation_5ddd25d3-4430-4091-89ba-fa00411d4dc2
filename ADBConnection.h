#ifndef ADBCONNECTION_H
#define ADBCONNECTION_H

#include <QObject>
#include <QProcess>
#include <QTimer>
#include <QVariantMap>
#include <QMutex>
#include <QStringList>
#include <QDateTime>

class ADBConnection : public QObject
{
    Q_OBJECT

public:
    enum ConnectionState {
        Disconnected,
        Connecting,
        Connected,
        Error
    };

    enum DeviceState {
        Unknown,
        Offline,
        Device,
        NoPermissions,
        Unauthorized,
        Bootloader,
        Recovery
    };

    struct DeviceInfo {
        QString serialNumber;
        QString model;
        QString product;
        QString device;
        DeviceState state;
        bool isEmulator;
        QString androidVersion;
        QString apiLevel;
    };

    explicit ADBConnection(QObject *parent = nullptr);
    ~ADBConnection();

    // 连接管理
    bool connect(const QVariantMap &params);
    void disconnect();
    bool isConnected() const;
    ConnectionState state() const;
    QString stateString() const;

    // 数据传输
    bool sendCommand(const QString &command);
    bool executeShellCommand(const QString &command);
    bool executeAdbCommand(const QStringList &args);
    
    // 设备管理
    void setDevice(const QString &deviceId);
    QString device() const;
    DeviceInfo deviceInfo() const;
    static QStringList getAvailableDevices();
    static QList<DeviceInfo> getDeviceList();
    
    // ADB路径配置
    void setAdbPath(const QString &path);
    QString adbPath() const;
    static QString findAdbPath();
    
    // 连接选项
    void setConnectionTimeout(int seconds);
    int connectionTimeout() const;
    void setAutoReconnect(bool enabled);
    bool autoReconnectEnabled() const;
    
    // 文件操作
    bool pushFile(const QString &localPath, const QString &remotePath);
    bool pullFile(const QString &remotePath, const QString &localPath);
    bool installApk(const QString &apkPath);
    bool uninstallPackage(const QString &packageName);
    
    // 系统信息
    QString getProperty(const QString &property);
    QVariantMap getSystemProperties();
    QStringList getInstalledPackages();
    QStringList getRunningProcesses();
    
    // 统计信息
    qint64 bytesReceived() const;
    qint64 bytesSent() const;
    QDateTime connectionTime() const;
    int commandCount() const;

public slots:
    void reconnect();
    void refreshDeviceInfo();

signals:
    void connected();
    void disconnected();
    void dataReceived(const QByteArray &data);
    void errorOccurred(const QString &error);
    void stateChanged(ConnectionState state);
    void deviceListChanged();
    void commandFinished(int exitCode, const QString &output);

private slots:
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onProcessError(QProcess::ProcessError error);
    void onProcessReadyRead();
    void onConnectionTimeout();
    void onDeviceCheckTimer();

private:
    void setState(ConnectionState state);
    bool startAdbProcess(const QStringList &arguments);
    void processOutput();
    bool checkAdbAvailable();
    bool checkDeviceConnected();
    DeviceInfo parseDeviceInfo(const QString &deviceLine);
    DeviceState parseDeviceState(const QString &stateStr);
    QString formatError(const QString &error) const;
    void cleanup();

private:
    // ADB进程
    QProcess *m_adbProcess;
    QString m_adbPath;
    
    // 设备信息
    QString m_deviceId;
    DeviceInfo m_deviceInfo;
    
    // 连接状态
    ConnectionState m_state;
    bool m_isConnected;
    QDateTime m_connectionTime;
    
    // 连接选项
    int m_connectionTimeout;
    bool m_autoReconnectEnabled;
    
    // 定时器
    QTimer *m_connectionTimer;
    QTimer *m_deviceCheckTimer;
    
    // 数据缓冲
    QByteArray m_outputBuffer;
    QByteArray m_errorBuffer;
    
    // 统计信息
    qint64 m_bytesReceived;
    qint64 m_bytesSent;
    int m_commandCount;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 当前命令
    QString m_currentCommand;
    QStringList m_commandQueue;
    bool m_isExecutingCommand;
};

#endif // ADBCONNECTION_H
