#include "InteractiveTerminal.h"
#include <QScrollBar>
#include <QTextCursor>
#include <QDateTime>
#include <QApplication>
#include <QClipboard>
#include <QMimeData>
#include <QKeyEvent>
#include <QContextMenuEvent>
#include <QMenu>
#include <QAction>
#include <QFileDialog>
#include <QMessageBox>

InteractiveTerminal::InteractiveTerminal(QWidget *parent)
    : QTextEdit(parent), m_timestampEnabled(true), m_timestampFormat("hh:mm:ss"), m_echoEnabled(true), m_autoScrollEnabled(true), m_maxLines(10000), m_currentLineCount(0), m_isReadOnly(false), m_highlightEnabled(true), m_wordWrapEnabled(true), m_fontFamily("Consolas"), m_fontSize(10), m_backgroundColor("#ffffff"), m_textColor("#000000"), m_inputColor("#0066cc"), m_outputColor("#006600"), m_errorColor("#cc0000"), m_systemColor("#666666"), m_warningColor("#ff6600"), m_debugColor("#9900cc")
{
    // 设置基本属性
    setReadOnly(false);
    setAcceptRichText(true);
    setUndoRedoEnabled(false);

    // 设置字体
    setupFont();

    // 设置样式
    setupStyles();

    // 创建上下文菜单
    setupContextMenu();

    // 连接信号
    connect(this, &QTextEdit::textChanged, this, &InteractiveTerminal::onTextChanged);

    // 显示欢迎信息
    showWelcomeMessage();
}

InteractiveTerminal::~InteractiveTerminal()
{
}

void InteractiveTerminal::setupFont()
{
    QFont font(m_fontFamily, m_fontSize);
    font.setFixedPitch(true);
    setFont(font);
}

void InteractiveTerminal::setupStyles()
{
    QString style = QString(
                        "QTextEdit {"
                        "    background-color: %1;"
                        "    color: %2;"
                        "    border: 1px solid #ccc;"
                        "    selection-background-color: #3399ff;"
                        "    selection-color: white;"
                        "}")
                        .arg(m_backgroundColor, m_textColor);

    setStyleSheet(style);

    // 设置自动换行
    setLineWrapMode(m_wordWrapEnabled ? QTextEdit::WidgetWidth : QTextEdit::NoWrap);
}

void InteractiveTerminal::setupContextMenu()
{
    setContextMenuPolicy(Qt::CustomContextMenu);
    connect(this, &QWidget::customContextMenuRequested, this, &InteractiveTerminal::showContextMenu);
}

void InteractiveTerminal::showWelcomeMessage()
{
    appendMessage("=== RF调试工具 Qt版本 ===", LogManager::System);
    appendMessage("欢迎使用RF调试工具！", LogManager::Info);
    appendMessage("请先连接设备，然后开始调试。", LogManager::Info);
    appendMessage("", LogManager::System);
}

void InteractiveTerminal::appendMessage(const QString &message, LogManager::LogLevel level)
{
    if (message.isEmpty())
    {
        append("");
        return;
    }

    QString formattedMessage = formatMessage(message, level);
    QString coloredMessage = applyColorFormatting(formattedMessage, level);

    // 移动光标到末尾
    QTextCursor cursor = textCursor();
    cursor.movePosition(QTextCursor::End);
    setTextCursor(cursor);

    // 插入格式化的消息
    insertHtml(coloredMessage + "<br>");

    // 限制行数
    limitLines();

    // 自动滚动到底部
    if (m_autoScrollEnabled)
    {
        scrollToBottom();
    }

    m_currentLineCount++;

    emit messageAppended(message, level);
}

void InteractiveTerminal::appendUserInput(const QString &input)
{
    QString prefix = m_echoEnabled ? ">>> " : "";
    appendMessage(prefix + input, LogManager::Input);
}

void InteractiveTerminal::appendSystemOutput(const QString &output)
{
    appendMessage(output, LogManager::Output);
}

void InteractiveTerminal::appendError(const QString &error)
{
    appendMessage("错误: " + error, LogManager::Error);
}

void InteractiveTerminal::appendWarning(const QString &warning)
{
    appendMessage("警告: " + warning, LogManager::Warning);
}

void InteractiveTerminal::appendDebug(const QString &debug)
{
    appendMessage("调试: " + debug, LogManager::Debug);
}

void InteractiveTerminal::clearTerminal()
{
    clear();
    m_currentLineCount = 0;
    showWelcomeMessage();
    emit terminalCleared();
}

void InteractiveTerminal::saveToFile()
{
    QString fileName = QFileDialog::getSaveFileName(this, "保存终端内容",
                                                    "terminal_output.txt",
                                                    "文本文件 (*.txt);;所有文件 (*.*)");
    if (!fileName.isEmpty())
    {
        saveToFile(fileName);
    }
}

bool InteractiveTerminal::saveToFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QMessageBox::warning(this, "保存失败", "无法创建文件: " + filePath);
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");
    out << toPlainText();

    file.close();

    appendMessage("终端内容已保存到: " + filePath, LogManager::Info);
    return true;
}

void InteractiveTerminal::setTimestampEnabled(bool enabled)
{
    m_timestampEnabled = enabled;
}

bool InteractiveTerminal::isTimestampEnabled() const
{
    return m_timestampEnabled;
}

void InteractiveTerminal::setTimestampFormat(const QString &format)
{
    m_timestampFormat = format;
}

QString InteractiveTerminal::timestampFormat() const
{
    return m_timestampFormat;
}

void InteractiveTerminal::setEchoEnabled(bool enabled)
{
    m_echoEnabled = enabled;
}

bool InteractiveTerminal::isEchoEnabled() const
{
    return m_echoEnabled;
}

void InteractiveTerminal::setAutoScrollEnabled(bool enabled)
{
    m_autoScrollEnabled = enabled;
}

bool InteractiveTerminal::isAutoScrollEnabled() const
{
    return m_autoScrollEnabled;
}

void InteractiveTerminal::setMaxLines(int maxLines)
{
    m_maxLines = qMax(100, maxLines);
    limitLines();
}

int InteractiveTerminal::maxLines() const
{
    return m_maxLines;
}

void InteractiveTerminal::setHighlightEnabled(bool enabled)
{
    m_highlightEnabled = enabled;
}

bool InteractiveTerminal::isHighlightEnabled() const
{
    return m_highlightEnabled;
}

void InteractiveTerminal::setWordWrapEnabled(bool enabled)
{
    m_wordWrapEnabled = enabled;
    setLineWrapMode(enabled ? QTextEdit::WidgetWidth : QTextEdit::NoWrap);
}

bool InteractiveTerminal::isWordWrapEnabled() const
{
    return m_wordWrapEnabled;
}

void InteractiveTerminal::setTerminalFont(const QString &family, int size)
{
    m_fontFamily = family;
    m_fontSize = size;
    setupFont();
}

QString InteractiveTerminal::fontFamily() const
{
    return m_fontFamily;
}

int InteractiveTerminal::fontSize() const
{
    return m_fontSize;
}

void InteractiveTerminal::setColorScheme(const QVariantMap &colors)
{
    m_backgroundColor = colors.value("background", "#ffffff").toString();
    m_textColor = colors.value("text", "#000000").toString();
    m_inputColor = colors.value("input", "#0066cc").toString();
    m_outputColor = colors.value("output", "#006600").toString();
    m_errorColor = colors.value("error", "#cc0000").toString();
    m_systemColor = colors.value("system", "#666666").toString();
    m_warningColor = colors.value("warning", "#ff6600").toString();
    m_debugColor = colors.value("debug", "#9900cc").toString();

    setupStyles();
}

QVariantMap InteractiveTerminal::colorScheme() const
{
    QVariantMap colors;
    colors["background"] = m_backgroundColor;
    colors["text"] = m_textColor;
    colors["input"] = m_inputColor;
    colors["output"] = m_outputColor;
    colors["error"] = m_errorColor;
    colors["system"] = m_systemColor;
    colors["warning"] = m_warningColor;
    colors["debug"] = m_debugColor;
    return colors;
}

void InteractiveTerminal::scrollToBottom()
{
    QScrollBar *scrollBar = verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

void InteractiveTerminal::scrollToTop()
{
    QScrollBar *scrollBar = verticalScrollBar();
    scrollBar->setValue(scrollBar->minimum());
}

QString InteractiveTerminal::formatMessage(const QString &message, LogManager::LogLevel level) const
{
    QString formatted = message;

    // 添加时间戳
    if (m_timestampEnabled)
    {
        QString timestamp = QDateTime::currentDateTime().toString(m_timestampFormat);
        formatted = QString("[%1] %2").arg(timestamp, message);
    }

    return formatted;
}

QString InteractiveTerminal::applyColorFormatting(const QString &message, LogManager::LogLevel level) const
{
    if (!m_highlightEnabled)
    {
        return message;
    }

    QString color;
    switch (level)
    {
    case LogManager::Input:
        color = m_inputColor;
        break;
    case LogManager::Output:
        color = m_outputColor;
        break;
    case LogManager::Error:
        color = m_errorColor;
        break;
    case LogManager::Warning:
        color = m_warningColor;
        break;
    case LogManager::Debug:
        color = m_debugColor;
        break;
    case LogManager::System:
        color = m_systemColor;
        break;
    default:
        color = m_textColor;
        break;
    }

    return QString("<span style=\"color: %1;\">%2</span>").arg(color, message.toHtmlEscaped());
}

void InteractiveTerminal::limitLines()
{
    if (m_currentLineCount <= m_maxLines)
    {
        return;
    }

    QTextCursor cursor = textCursor();
    cursor.movePosition(QTextCursor::Start);

    // 删除多余的行
    int linesToRemove = m_currentLineCount - m_maxLines;
    for (int i = 0; i < linesToRemove; ++i)
    {
        cursor.select(QTextCursor::LineUnderCursor);
        cursor.removeSelectedText();
        cursor.deleteChar(); // 删除换行符
    }

    m_currentLineCount = m_maxLines;
}

void InteractiveTerminal::onTextChanged()
{
    // 这里可以添加文本变化时的处理逻辑
}

void InteractiveTerminal::showContextMenu(const QPoint &pos)
{
    QMenu contextMenu(this);

    // 复制
    QAction *copyAction = contextMenu.addAction("复制");
    copyAction->setEnabled(textCursor().hasSelection());
    connect(copyAction, &QAction::triggered, this, &InteractiveTerminal::copy);

    // 全选
    QAction *selectAllAction = contextMenu.addAction("全选");
    connect(selectAllAction, &QAction::triggered, this, &InteractiveTerminal::selectAll);

    contextMenu.addSeparator();

    // 清空
    QAction *clearAction = contextMenu.addAction("清空终端");
    connect(clearAction, &QAction::triggered, this, &InteractiveTerminal::clearTerminal);

    // 保存
    QAction *saveAction = contextMenu.addAction("保存到文件...");
    connect(saveAction, &QAction::triggered, this, &InteractiveTerminal::saveToFile);

    contextMenu.addSeparator();

    // 滚动选项
    QAction *scrollToTopAction = contextMenu.addAction("滚动到顶部");
    connect(scrollToTopAction, &QAction::triggered, this, &InteractiveTerminal::scrollToTop);

    QAction *scrollToBottomAction = contextMenu.addAction("滚动到底部");
    connect(scrollToBottomAction, &QAction::triggered, this, &InteractiveTerminal::scrollToBottom);

    contextMenu.addSeparator();

    // 自动滚动开关
    QAction *autoScrollAction = contextMenu.addAction("自动滚动");
    autoScrollAction->setCheckable(true);
    autoScrollAction->setChecked(m_autoScrollEnabled);
    connect(autoScrollAction, &QAction::triggered, this, &InteractiveTerminal::setAutoScrollEnabled);

    // 时间戳开关
    QAction *timestampAction = contextMenu.addAction("显示时间戳");
    timestampAction->setCheckable(true);
    timestampAction->setChecked(m_timestampEnabled);
    connect(timestampAction, &QAction::triggered, this, &InteractiveTerminal::setTimestampEnabled);

    // 语法高亮开关
    QAction *highlightAction = contextMenu.addAction("语法高亮");
    highlightAction->setCheckable(true);
    highlightAction->setChecked(m_highlightEnabled);
    connect(highlightAction, &QAction::triggered, this, &InteractiveTerminal::setHighlightEnabled);

    // 自动换行开关
    QAction *wordWrapAction = contextMenu.addAction("自动换行");
    wordWrapAction->setCheckable(true);
    wordWrapAction->setChecked(m_wordWrapEnabled);
    connect(wordWrapAction, &QAction::triggered, this, &InteractiveTerminal::setWordWrapEnabled);

    contextMenu.exec(mapToGlobal(pos));
}

void InteractiveTerminal::keyPressEvent(QKeyEvent *event)
{
    // 处理特殊按键
    if (event->key() == Qt::Key_L && event->modifiers() == Qt::ControlModifier)
    {
        // Ctrl+L 清空终端
        clearTerminal();
        return;
    }

    if (event->key() == Qt::Key_C && event->modifiers() == Qt::ControlModifier)
    {
        // Ctrl+C 复制选中文本
        if (textCursor().hasSelection())
        {
            copy();
            return;
        }
    }

    if (event->key() == Qt::Key_A && event->modifiers() == Qt::ControlModifier)
    {
        // Ctrl+A 全选
        selectAll();
        return;
    }

    if (event->key() == Qt::Key_S && event->modifiers() == Qt::ControlModifier)
    {
        // Ctrl+S 保存
        saveToFile();
        return;
    }

    if (event->key() == Qt::Key_Home && event->modifiers() == Qt::ControlModifier)
    {
        // Ctrl+Home 滚动到顶部
        scrollToTop();
        return;
    }

    if (event->key() == Qt::Key_End && event->modifiers() == Qt::ControlModifier)
    {
        // Ctrl+End 滚动到底部
        scrollToBottom();
        return;
    }

    // 如果是只读模式，只允许导航和复制操作
    if (m_isReadOnly)
    {
        if (event->key() == Qt::Key_Up || event->key() == Qt::Key_Down ||
            event->key() == Qt::Key_Left || event->key() == Qt::Key_Right ||
            event->key() == Qt::Key_PageUp || event->key() == Qt::Key_PageDown ||
            event->key() == Qt::Key_Home || event->key() == Qt::Key_End)
        {
            QTextEdit::keyPressEvent(event);
        }
        return;
    }

    QTextEdit::keyPressEvent(event);
}

void InteractiveTerminal::wheelEvent(QWheelEvent *event)
{
    // 处理鼠标滚轮事件
    if (event->modifiers() == Qt::ControlModifier)
    {
        // Ctrl+滚轮 调整字体大小
        int delta = event->angleDelta().y();
        if (delta > 0)
        {
            // 放大字体
            setTerminalFont(m_fontFamily, qMin(m_fontSize + 1, 20));
        }
        else if (delta < 0)
        {
            // 缩小字体
            setTerminalFont(m_fontFamily, qMax(m_fontSize - 1, 8));
        }
        event->accept();
        return;
    }

    QTextEdit::wheelEvent(event);
}

void InteractiveTerminal::resizeEvent(QResizeEvent *event)
{
    QTextEdit::resizeEvent(event);

    // 如果启用了自动滚动，调整大小后滚动到底部
    if (m_autoScrollEnabled)
    {
        QTimer::singleShot(0, this, &InteractiveTerminal::scrollToBottom);
    }
}

void InteractiveTerminal::insertFromMimeData(const QMimeData *source)
{
    // 处理粘贴操作
    if (m_isReadOnly)
    {
        return;
    }

    if (source->hasText())
    {
        QString text = source->text();

        // 处理多行文本
        QStringList lines = text.split('\n');
        for (const QString &line : lines)
        {
            if (!line.isEmpty())
            {
                appendUserInput(line);
                emit commandEntered(line);
            }
        }
    }
}

// 公共槽函数
void InteractiveTerminal::copy()
{
    QTextEdit::copy();
}

void InteractiveTerminal::selectAll()
{
    QTextEdit::selectAll();
}

void InteractiveTerminal::find(const QString &text, bool caseSensitive, bool wholeWords)
{
    QTextDocument::FindFlags flags;
    if (caseSensitive)
    {
        flags |= QTextDocument::FindCaseSensitively;
    }
    if (wholeWords)
    {
        flags |= QTextDocument::FindWholeWords;
    }

    QTextCursor cursor = textCursor();
    cursor = document()->find(text, cursor, flags);

    if (!cursor.isNull())
    {
        setTextCursor(cursor);
    }
}

void InteractiveTerminal::findNext(const QString &text, bool caseSensitive, bool wholeWords)
{
    find(text, caseSensitive, wholeWords);
}

void InteractiveTerminal::findPrevious(const QString &text, bool caseSensitive, bool wholeWords)
{
    QTextDocument::FindFlags flags = QTextDocument::FindBackward;
    if (caseSensitive)
    {
        flags |= QTextDocument::FindCaseSensitively;
    }
    if (wholeWords)
    {
        flags |= QTextDocument::FindWholeWords;
    }

    QTextCursor cursor = textCursor();
    cursor = document()->find(text, cursor, flags);

    if (!cursor.isNull())
    {
        setTextCursor(cursor);
    }
}

void InteractiveTerminal::setReadOnlyMode(bool readOnly)
{
    m_isReadOnly = readOnly;
    setReadOnly(readOnly);
}

bool InteractiveTerminal::isReadOnlyMode() const
{
    return m_isReadOnly;
}

int InteractiveTerminal::currentLineCount() const
{
    return m_currentLineCount;
}

void InteractiveTerminal::exportToHtml(const QString &filePath)
{
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        out << toHtml();
        file.close();

        appendMessage("终端内容已导出为HTML: " + filePath, LogManager::Info);
    }
    else
    {
        appendError("无法导出HTML文件: " + filePath);
    }
}

void InteractiveTerminal::importFromFile(const QString &filePath)
{
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream in(&file);
        in.setCodec("UTF-8");

        while (!in.atEnd())
        {
            QString line = in.readLine();
            appendMessage(line, LogManager::System);
        }

        file.close();
        appendMessage("已导入文件内容: " + filePath, LogManager::Info);
    }
    else
    {
        appendError("无法读取文件: " + filePath);
    }
}
