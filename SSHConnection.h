#ifndef SSHCONNECTION_H
#define SSHCONNECTION_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QVariantMap>
#include <QMutex>
#include <QQueue>
#include <QDateTime>

class SSHConnection : public QObject
{
    Q_OBJECT

public:
    enum AuthMethod {
        Password,
        PublicKey,
        KeyboardInteractive
    };

    enum ConnectionState {
        Disconnected,
        Connecting,
        Authenticating,
        Connected,
        Error
    };

    explicit SSHConnection(QObject *parent = nullptr);
    ~SSHConnection();

    // 连接管理
    bool connect(const QVariantMap &params);
    void disconnect();
    bool isConnected() const;
    ConnectionState state() const;
    QString stateString() const;

    // 数据传输
    bool sendCommand(const QString &command);
    bool sendData(const QByteArray &data);
    
    // 配置
    void setHost(const QString &host);
    QString host() const;
    void setPort(int port);
    int port() const;
    void setUsername(const QString &username);
    QString username() const;
    void setPassword(const QString &password);
    void setPrivateKeyFile(const QString &keyFile);
    QString privateKeyFile() const;
    void setAuthMethod(AuthMethod method);
    AuthMethod authMethod() const;
    
    // 连接选项
    void setConnectionTimeout(int seconds);
    int connectionTimeout() const;
    void setKeepAlive(bool enabled);
    bool keepAliveEnabled() const;
    void setKeepAliveInterval(int seconds);
    int keepAliveInterval() const;
    
    // 终端选项
    void setTerminalType(const QString &termType);
    QString terminalType() const;
    void setTerminalSize(int width, int height);
    QSize terminalSize() const;
    
    // 统计信息
    qint64 bytesReceived() const;
    qint64 bytesSent() const;
    QDateTime connectionTime() const;
    int commandCount() const;

public slots:
    void reconnect();
    void sendKeepAlive();

signals:
    void connected();
    void disconnected();
    void dataReceived(const QByteArray &data);
    void errorOccurred(const QString &error);
    void stateChanged(ConnectionState state);
    void authenticationRequired(AuthMethod method);
    void hostKeyVerificationRequired(const QString &hostKey);

private slots:
    void onSocketConnected();
    void onSocketDisconnected();
    void onSocketError(QAbstractSocket::SocketError error);
    void onSocketReadyRead();
    void onConnectionTimeout();
    void onKeepAliveTimer();

private:
    void setState(ConnectionState state);
    void processReceivedData();
    bool authenticatePassword();
    bool authenticatePublicKey();
    bool loadPrivateKey(const QString &keyFile);
    void setupKeepAlive();
    void cleanup();
    QString formatError(const QString &error) const;

private:
    // 网络连接
    QTcpSocket *m_socket;
    QString m_host;
    int m_port;
    
    // 认证信息
    QString m_username;
    QString m_password;
    QString m_privateKeyFile;
    AuthMethod m_authMethod;
    
    // 连接状态
    ConnectionState m_state;
    bool m_isConnected;
    QDateTime m_connectionTime;
    
    // 连接选项
    int m_connectionTimeout;
    bool m_keepAliveEnabled;
    int m_keepAliveInterval;
    
    // 终端选项
    QString m_terminalType;
    QSize m_terminalSize;
    
    // 定时器
    QTimer *m_connectionTimer;
    QTimer *m_keepAliveTimer;
    
    // 数据缓冲
    QByteArray m_receiveBuffer;
    QQueue<QByteArray> m_sendQueue;
    
    // 统计信息
    qint64 m_bytesReceived;
    qint64 m_bytesSent;
    int m_commandCount;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // SSH协议相关（简化实现）
    bool m_protocolInitialized;
    bool m_authenticated;
    QString m_sessionId;
};

#endif // SSHCONNECTION_H
