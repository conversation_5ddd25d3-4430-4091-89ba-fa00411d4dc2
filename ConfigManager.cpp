#include "ConfigManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QMutexLocker>
#include <QDebug>
#include <QSettings>
#include <QTimer>
#include <QFileSystemWatcher>
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonValue>
#include <QFileInfo>

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent), m_settings(nullptr), m_autoSaveEnabled(true), m_autoSaveInterval(30), m_hasUnsavedChanges(false), m_backupEnabled(true), m_maxBackupFiles(5), m_validationEnabled(true), m_ignoreFileChanges(false), m_maxHistorySize(100)
{
    // 设置默认配置文件路径
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(dataDir);
    m_configFilePath = QDir(dataDir).filePath("config.ini");

    // 初始化设置对象
    m_settings = new QSettings(m_configFilePath, QSettings::IniFormat, this);

    // 初始化默认值
    initializeDefaults();

    // 设置自动保存
    setupAutoSave();

    // 设置文件监控
    setupFileWatcher();

    // 加载配置
    load();
}

ConfigManager::~ConfigManager()
{
    if (m_autoSaveEnabled && m_hasUnsavedChanges)
    {
        save();
    }
}

void ConfigManager::initializeDefaults()
{
    // 应用程序默认配置
    m_defaultValues["app/version"] = "2.0.0";
    m_defaultValues["app/language"] = "zh_CN";
    m_defaultValues["app/theme"] = "default";

    // 窗口默认配置
    m_defaultValues["window/width"] = 1200;
    m_defaultValues["window/height"] = 800;
    m_defaultValues["window/maximized"] = false;

    // 连接默认配置
    m_defaultValues["connection/timeout"] = 30;
    m_defaultValues["connection/auto_reconnect"] = false;
    m_defaultValues["connection/max_reconnect_attempts"] = 3;

    // 日志默认配置
    m_defaultValues["log/timestamp_enabled"] = true;
    m_defaultValues["log/timestamp_format"] = "yyyy-MM-dd hh:mm:ss";
    m_defaultValues["log/echo_enabled"] = true;
    m_defaultValues["log/file_enabled"] = false;
    m_defaultValues["log/file_path"] = "";
    m_defaultValues["log/auto_save"] = true;
    m_defaultValues["log/auto_save_interval"] = 30;
    m_defaultValues["log/level"] = 1; // Info
    m_defaultValues["log/max_lines"] = 10000;
    m_defaultValues["log/rotation_enabled"] = false;
    m_defaultValues["log/max_file_size"] = 10; // MB
    m_defaultValues["log/max_backup_files"] = 5;

    // 终端默认配置
    m_defaultValues["terminal/timestamp_enabled"] = true;
    m_defaultValues["terminal/timestamp_format"] = "hh:mm:ss";
    m_defaultValues["terminal/echo_enabled"] = true;
    m_defaultValues["terminal/max_history"] = 1000;

    // 背景默认配置
    m_defaultValues["background/type"] = "color";
    m_defaultValues["background/color"] = "#f8f9fa";
    m_defaultValues["background/color2"] = "#e9ecef";
    m_defaultValues["background/image"] = "";
    m_defaultValues["background/opacity"] = 1.0;

    // 快捷命令默认配置
    m_defaultValues["quick_command_1"] = "ls -la";
    m_defaultValues["quick_command_1_name"] = "列表";
    m_defaultValues["quick_command_2"] = "pwd";
    m_defaultValues["quick_command_2_name"] = "路径";
    m_defaultValues["quick_command_3"] = "whoami";
    m_defaultValues["quick_command_3_name"] = "用户";
}

void ConfigManager::setupAutoSave()
{
    m_autoSaveTimer = new QTimer(this);
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
    connect(m_autoSaveTimer, &QTimer::timeout, this, &ConfigManager::onAutoSaveTimer);

    if (m_autoSaveEnabled)
    {
        m_autoSaveTimer->start();
    }
}

void ConfigManager::setupFileWatcher()
{
    m_fileWatcher = new QFileSystemWatcher(this);
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged,
            this, &ConfigManager::onConfigFileChanged);

    if (QFile::exists(m_configFilePath))
    {
        m_fileWatcher->addPath(m_configFilePath);
    }
}

void ConfigManager::setValue(const QString &key, const QVariant &value)
{
    QMutexLocker locker(&m_mutex);

    // 验证值
    if (m_validationEnabled && !validateValue(key, value))
    {
        qWarning() << "配置值验证失败:" << key << value;
        return;
    }

    // 清理值
    QVariant sanitizedValue = sanitizeValue(key, value);

    // 检查是否有变化
    QVariant currentValue = getValue(key);
    if (currentValue == sanitizedValue)
    {
        return;
    }

    // 保存到内存
    m_configData[key] = sanitizedValue;

    // 保存到设置文件
    if (m_settings)
    {
        m_settings->setValue(key, sanitizedValue);
    }

    m_hasUnsavedChanges = true;

    // 通知配置变化
    notifyConfigChange(key, sanitizedValue);
}

QVariant ConfigManager::getValue(const QString &key, const QVariant &defaultValue) const
{
    QMutexLocker locker(&m_mutex);

    // 首先从内存中查找
    if (m_configData.contains(key))
    {
        return m_configData[key];
    }

    // 然后从设置文件中查找
    if (m_settings)
    {
        QVariant value = m_settings->value(key);
        if (value.isValid())
        {
            return value;
        }
    }

    // 最后使用默认值
    if (!defaultValue.isNull())
    {
        return defaultValue;
    }

    // 使用预定义的默认值
    if (m_defaultValues.contains(key))
    {
        return m_defaultValues[key];
    }

    return QVariant();
}

bool ConfigManager::contains(const QString &key) const
{
    QMutexLocker locker(&m_mutex);

    return m_configData.contains(key) ||
           (m_settings && m_settings->contains(key)) ||
           m_defaultValues.contains(key);
}

void ConfigManager::removeKey(const QString &key)
{
    QMutexLocker locker(&m_mutex);

    m_configData.remove(key);

    if (m_settings)
    {
        m_settings->remove(key);
    }

    m_hasUnsavedChanges = true;

    notifyConfigChange(key, QVariant());
}

QStringList ConfigManager::allKeys() const
{
    QMutexLocker locker(&m_mutex);

    QStringList keys;

    // 从内存配置中获取
    keys.append(m_configData.keys());

    // 从设置文件中获取
    if (m_settings)
    {
        keys.append(m_settings->allKeys());
    }

    // 从默认值中获取
    keys.append(m_defaultValues.keys());

    // 去重并排序
    keys.removeDuplicates();
    keys.sort();

    return keys;
}

void ConfigManager::beginGroup(const QString &prefix)
{
    m_groupStack.append(prefix);

    if (m_settings)
    {
        m_settings->beginGroup(prefix);
    }
}

void ConfigManager::endGroup()
{
    if (!m_groupStack.isEmpty())
    {
        m_groupStack.removeLast();
    }

    if (m_settings)
    {
        m_settings->endGroup();
    }
}

QString ConfigManager::group() const
{
    return m_groupStack.join("/");
}

QStringList ConfigManager::childGroups() const
{
    if (m_settings)
    {
        return m_settings->childGroups();
    }
    return QStringList();
}

QStringList ConfigManager::childKeys() const
{
    if (m_settings)
    {
        return m_settings->childKeys();
    }
    return QStringList();
}

void ConfigManager::setValues(const QVariantMap &values)
{
    for (auto it = values.begin(); it != values.end(); ++it)
    {
        setValue(it.key(), it.value());
    }
}

QVariantMap ConfigManager::getValues(const QStringList &keys) const
{
    QVariantMap values;
    for (const QString &key : keys)
    {
        values[key] = getValue(key);
    }
    return values;
}

QVariantMap ConfigManager::getAllValues() const
{
    QMutexLocker locker(&m_mutex);

    QVariantMap allValues;

    // 添加默认值
    for (auto it = m_defaultValues.begin(); it != m_defaultValues.end(); ++it)
    {
        allValues[it.key()] = it.value();
    }

    // 添加设置文件中的值
    if (m_settings)
    {
        QStringList keys = m_settings->allKeys();
        for (const QString &key : keys)
        {
            allValues[key] = m_settings->value(key);
        }
    }

    // 添加内存中的值（覆盖之前的值）
    for (auto it = m_configData.begin(); it != m_configData.end(); ++it)
    {
        allValues[it.key()] = it.value();
    }

    return allValues;
}

void ConfigManager::setConfigFile(const QString &filePath)
{
    QMutexLocker locker(&m_mutex);

    if (m_configFilePath == filePath)
    {
        return;
    }

    // 保存当前配置
    if (m_hasUnsavedChanges)
    {
        save();
    }

    // 移除旧文件监控
    if (m_fileWatcher && QFile::exists(m_configFilePath))
    {
        m_fileWatcher->removePath(m_configFilePath);
    }

    m_configFilePath = filePath;

    // 重新创建设置对象
    if (m_settings)
    {
        m_settings->deleteLater();
    }
    m_settings = new QSettings(m_configFilePath, QSettings::IniFormat, this);

    // 添加新文件监控
    if (m_fileWatcher && QFile::exists(m_configFilePath))
    {
        m_fileWatcher->addPath(m_configFilePath);
    }

    // 重新加载配置
    load();
}

QString ConfigManager::configFile() const
{
    return m_configFilePath;
}

bool ConfigManager::loadFromFile(const QString &filePath)
{
    QString path = filePath.isEmpty() ? m_configFilePath : filePath;

    QSettings settings(path, QSettings::IniFormat);
    if (settings.status() != QSettings::NoError)
    {
        return false;
    }

    QMutexLocker locker(&m_mutex);

    // 清空当前配置
    m_configData.clear();

    // 加载所有键值
    QStringList keys = settings.allKeys();
    for (const QString &key : keys)
    {
        m_configData[key] = settings.value(key);
    }

    m_hasUnsavedChanges = false;
    emit configLoaded();

    return true;
}

bool ConfigManager::saveToFile(const QString &filePath)
{
    QString path = filePath.isEmpty() ? m_configFilePath : filePath;

    QSettings settings(path, QSettings::IniFormat);

    QMutexLocker locker(&m_mutex);

    // 保存所有配置
    for (auto it = m_configData.begin(); it != m_configData.end(); ++it)
    {
        settings.setValue(it.key(), it.value());
    }

    settings.sync();

    if (settings.status() != QSettings::NoError)
    {
        emit configError("保存配置文件失败: " + path);
        return false;
    }

    m_hasUnsavedChanges = false;
    emit configSaved();

    return true;
}

void ConfigManager::reloadConfig()
{
    loadFromFile();
}

void ConfigManager::setAutoSaveEnabled(bool enabled)
{
    m_autoSaveEnabled = enabled;

    if (enabled)
    {
        m_autoSaveTimer->start();
    }
    else
    {
        m_autoSaveTimer->stop();
    }
}

bool ConfigManager::isAutoSaveEnabled() const
{
    return m_autoSaveEnabled;
}

void ConfigManager::setAutoSaveInterval(int seconds)
{
    m_autoSaveInterval = qMax(1, seconds);
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
}

int ConfigManager::autoSaveInterval() const
{
    return m_autoSaveInterval;
}

void ConfigManager::setBackupEnabled(bool enabled)
{
    m_backupEnabled = enabled;
}

bool ConfigManager::isBackupEnabled() const
{
    return m_backupEnabled;
}

void ConfigManager::setMaxBackupFiles(int count)
{
    m_maxBackupFiles = qMax(1, count);
}

int ConfigManager::maxBackupFiles() const
{
    return m_maxBackupFiles;
}

bool ConfigManager::createBackup()
{
    if (!m_backupEnabled)
    {
        return false;
    }

    QString backupFile = getBackupFileName();
    bool success = QFile::copy(m_configFilePath, backupFile);

    if (success)
    {
        cleanupOldBackups();
    }

    return success;
}

void ConfigManager::setDefaultValues(const QVariantMap &defaults)
{
    QMutexLocker locker(&m_mutex);
    m_defaultValues = defaults;
}

QVariantMap ConfigManager::defaultValues() const
{
    QMutexLocker locker(&m_mutex);
    return m_defaultValues;
}

void ConfigManager::resetToDefaults()
{
    QMutexLocker locker(&m_mutex);

    m_configData = m_defaultValues;

    if (m_settings)
    {
        m_settings->clear();
        for (auto it = m_defaultValues.begin(); it != m_defaultValues.end(); ++it)
        {
            m_settings->setValue(it.key(), it.value());
        }
    }

    m_hasUnsavedChanges = true;

    // 通知所有配置重置
    for (auto it = m_defaultValues.begin(); it != m_defaultValues.end(); ++it)
    {
        notifyConfigChange(it.key(), it.value());
    }
}

void ConfigManager::resetKey(const QString &key)
{
    if (m_defaultValues.contains(key))
    {
        setValue(key, m_defaultValues[key]);
    }
    else
    {
        removeKey(key);
    }
}

void ConfigManager::save()
{
    saveToFile();
}

void ConfigManager::load()
{
    loadFromFile();
}

void ConfigManager::sync()
{
    if (m_settings)
    {
        m_settings->sync();
    }
}

void ConfigManager::onAutoSaveTimer()
{
    if (m_hasUnsavedChanges)
    {
        save();
    }
}

void ConfigManager::onConfigFileChanged(const QString &path)
{
    if (m_ignoreFileChanges)
    {
        return;
    }

    // 重新加载配置
    reloadConfig();
}

QString ConfigManager::getBackupFileName() const
{
    QFileInfo fileInfo(m_configFilePath);
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    return QString("%1/%2_%3.%4")
        .arg(fileInfo.absolutePath())
        .arg(fileInfo.baseName())
        .arg(timestamp)
        .arg(fileInfo.suffix());
}

void ConfigManager::cleanupOldBackups()
{
    QFileInfo fileInfo(m_configFilePath);
    QDir dir(fileInfo.absolutePath());

    QString pattern = QString("%1_*.%2").arg(fileInfo.baseName(), fileInfo.suffix());
    QStringList backupFiles = dir.entryList(QStringList() << pattern, QDir::Files, QDir::Time);

    // 删除多余的备份文件
    while (backupFiles.size() > m_maxBackupFiles)
    {
        QString oldestFile = backupFiles.takeLast();
        dir.remove(oldestFile);
    }
}

bool ConfigManager::validateValue(const QString &key, const QVariant &value) const
{
    // 简单的验证逻辑
    Q_UNUSED(key)
    Q_UNUSED(value)
    return true;
}

QVariant ConfigManager::sanitizeValue(const QString &key, const QVariant &value) const
{
    // 简单的值清理逻辑
    Q_UNUSED(key)
    return value;
}

void ConfigManager::notifyConfigChange(const QString &key, const QVariant &value)
{
    emit configChanged(key, value);
}

// 命令配置相关方法
void ConfigManager::setCommands(const QVariantList &commands)
{
    setValue("commands", commands);
}

QVariantList ConfigManager::getCommands() const
{
    return getValue("commands", QVariantList()).toList();
}

void ConfigManager::addCommand(const QVariantMap &command)
{
    QVariantList commands = getCommands();
    commands.append(command);
    setCommands(commands);
}

void ConfigManager::removeCommand(const QString &name)
{
    QVariantList commands = getCommands();
    for (int i = 0; i < commands.size(); ++i)
    {
        QVariantMap command = commands[i].toMap();
        if (command["name"].toString() == name)
        {
            commands.removeAt(i);
            break;
        }
    }
    setCommands(commands);
}

void ConfigManager::updateCommand(const QString &name, const QVariantMap &command)
{
    QVariantList commands = getCommands();
    for (int i = 0; i < commands.size(); ++i)
    {
        QVariantMap cmd = commands[i].toMap();
        if (cmd["name"].toString() == name)
        {
            commands[i] = command;
            break;
        }
    }
    setCommands(commands);
}

QVariantMap ConfigManager::getCommand(const QString &name) const
{
    QVariantList commands = getCommands();
    for (const QVariant &var : commands)
    {
        QVariantMap command = var.toMap();
        if (command["name"].toString() == name)
        {
            return command;
        }
    }
    return QVariantMap();
}

// 连接历史相关方法
void ConfigManager::addConnectionHistory(const QVariantMap &connection)
{
    QVariantList history = getValue("connection_history", QVariantList()).toList();

    // 检查是否已存在相同的连接
    for (int i = 0; i < history.size(); ++i)
    {
        QVariantMap existing = history[i].toMap();
        if (existing["type"] == connection["type"] &&
            existing["host"] == connection["host"] &&
            existing["port"] == connection["port"])
        {
            history.removeAt(i);
            break;
        }
    }

    // 添加到开头
    history.prepend(connection);

    // 限制历史记录数量
    while (history.size() > m_maxHistorySize)
    {
        history.removeLast();
    }

    setValue("connection_history", history);
}

QVariantList ConfigManager::getConnectionHistory() const
{
    return getValue("connection_history", QVariantList()).toList();
}

void ConfigManager::clearConnectionHistory()
{
    setValue("connection_history", QVariantList());
}

void ConfigManager::setMaxHistorySize(int size)
{
    m_maxHistorySize = qMax(1, size);

    // 如果当前历史记录超过新的限制，进行裁剪
    QVariantList history = getConnectionHistory();
    if (history.size() > m_maxHistorySize)
    {
        while (history.size() > m_maxHistorySize)
        {
            history.removeLast();
        }
        setValue("connection_history", history);
    }
}

// 窗口状态相关方法
void ConfigManager::saveWindowState(const QString &windowName, const QByteArray &state)
{
    setValue(QString("window_states/%1").arg(windowName), state);
}

QByteArray ConfigManager::getWindowState(const QString &windowName) const
{
    return getValue(QString("window_states/%1").arg(windowName)).toByteArray();
}

void ConfigManager::saveWindowGeometry(const QString &windowName, const QByteArray &geometry)
{
    setValue(QString("window_geometry/%1").arg(windowName), geometry);
}

QByteArray ConfigManager::getWindowGeometry(const QString &windowName) const
{
    return getValue(QString("window_geometry/%1").arg(windowName)).toByteArray();
}

// 导入导出相关方法
bool ConfigManager::exportConfig(const QString &filePath, const QStringList &keys) const
{
    QJsonObject rootObj;

    QStringList exportKeys = keys.isEmpty() ? allKeys() : keys;

    for (const QString &key : exportKeys)
    {
        QVariant value = getValue(key);
        if (value.isValid())
        {
            rootObj[key] = QJsonValue::fromVariant(value);
        }
    }

    QJsonDocument doc(rootObj);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly))
    {
        return false;
    }

    file.write(doc.toJson());
    file.close();

    return true;
}

bool ConfigManager::importConfig(const QString &filePath, bool merge)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError)
    {
        return false;
    }

    return fromJson(doc.object());
}

QJsonObject ConfigManager::toJson() const
{
    QJsonObject obj;
    QVariantMap allValues = getAllValues();

    for (auto it = allValues.begin(); it != allValues.end(); ++it)
    {
        obj[it.key()] = QJsonValue::fromVariant(it.value());
    }

    return obj;
}

bool ConfigManager::fromJson(const QJsonObject &json)
{
    QMutexLocker locker(&m_mutex);

    for (auto it = json.begin(); it != json.end(); ++it)
    {
        setValue(it.key(), it.value().toVariant());
    }

    return true;
}
