#include <QApplication>
#include <QMainWindow>
#include <QLabel>
#include <QVBoxLayout>
#include <QWidget>
#include <QDebug>

int main(int argc, char *argv[])
{
    // 设置高DPI属性
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    QApplication app(argc, argv);
    
    qDebug() << "Creating test window...";
    
    // 创建简单的主窗口
    QMainWindow window;
    window.setWindowTitle("RF调试工具 - 测试窗口");
    window.setMinimumSize(800, 600);
    
    // 创建中央部件
    QWidget *centralWidget = new QWidget;
    window.setCentralWidget(centralWidget);
    
    // 创建布局和标签
    QVBoxLayout *layout = new QVBoxLayout(centralWidget);
    QLabel *label = new QLabel("RF调试工具测试窗口\n\n如果您能看到这个窗口，说明Qt环境正常工作。");
    label->setAlignment(Qt::AlignCenter);
    label->setStyleSheet("font-size: 16px; padding: 20px;");
    
    layout->addWidget(label);
    
    // 显示窗口
    window.show();
    window.raise();
    window.activateWindow();
    
    qDebug() << "Test window shown";
    qDebug() << "Window visible:" << window.isVisible();
    qDebug() << "Window size:" << window.size();
    
    return app.exec();
}
