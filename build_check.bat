@echo off
echo ========================================
echo RF Tool Qt 编译检查脚本
echo ========================================
echo.

echo 检查Qt环境...
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到qmake，请确保Qt已正确安装并添加到PATH
    pause
    exit /b 1
)

echo Qt环境检查通过
echo.

echo 清理之前的构建文件...
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist build rmdir /s /q build
if exist bin rmdir /s /q bin

echo 生成Makefile...
qmake RFTool_Qt.pro
if %errorlevel% neq 0 (
    echo 错误: qmake执行失败
    pause
    exit /b 1
)

echo Makefile生成成功
echo.

echo 开始编译...
echo 注意: 如果出现编译错误，请检查以下常见问题：
echo 1. Qt版本是否为6.0或更高
echo 2. 是否缺少必要的Qt模块 (widgets, serialport, network)
echo 3. 编译器是否支持C++17
echo.

nmake
if %errorlevel% neq 0 (
    echo.
    echo 编译失败，请检查上面的错误信息
    echo.
    echo 常见解决方案：
    echo 1. 确保所有.h和.cpp文件都存在
    echo 2. 检查语法错误
    echo 3. 确保Qt模块正确安装
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译成功！
echo 可执行文件位置: bin\RFTool_Qt.exe
echo ========================================
pause
